import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    console.log('Fetching all invoices');

    // Get all invoices with project information
    const { data, error } = await supabase
      .from('invoices')
      .select(`
        *,
        project:projects(
          id,
          name,
          client_name
        )
      `)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching invoices:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch invoices', details: error.message },
        { status: 500 }
      );
    }

    console.log('Invoices fetched successfully:', data?.length || 0, 'invoices');

    return NextResponse.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error in invoices GET:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Creating invoice:', body);

    const {
      project_id,
      invoice_number,
      amount,
      currency = 'USD',
      status = 'draft',
      due_date,
      notes
    } = body;

    // Validate required fields
    if (!project_id || !invoice_number || !amount) {
      return NextResponse.json(
        { success: false, error: 'Project ID, invoice number, and amount are required' },
        { status: 400 }
      );
    }

    // Get user ID from the project
    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', project_id)
      .single();

    if (!project) {
      return NextResponse.json(
        { success: false, error: 'Project not found' },
        { status: 404 }
      );
    }

    const invoiceData = {
      project_id,
      user_id: project.user_id,
      invoice_number,
      amount: parseFloat(amount),
      currency,
      status,
      due_date: due_date || null,
      notes: notes || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('Inserting invoice data:', invoiceData);

    const { data, error } = await supabase
      .from('invoices')
      .insert([invoiceData])
      .select(`
        *,
        project:projects(
          id,
          name,
          client_name
        )
      `)
      .single();

    if (error) {
      console.error('Error creating invoice:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create invoice', details: error.message },
        { status: 500 }
      );
    }

    console.log('Invoice created successfully:', data);

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in invoices POST:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
