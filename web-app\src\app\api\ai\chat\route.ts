import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const { message, currentDocument, context } = await request.json();

    if (!message || !currentDocument) {
      return NextResponse.json(
        { error: 'Message and current document are required' },
        { status: 400 }
      );
    }

    const prompt = `
You are an AI assistant helping to improve a business document. 

Current Document:
${currentDocument}

Context: ${context}

User Request: ${message}

Please provide a helpful response and if the user is asking to modify the document, provide the improved version. If you're modifying the document, return the complete updated document with the requested changes.

Be professional, helpful, and focus on improving the document quality.
`;

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are a professional business document assistant. Help users improve their documents with expert advice and modifications."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 1500
    });

    const response = completion.choices[0]?.message?.content;
    if (!response) {
      throw new Error('No response from OpenAI');
    }

    return NextResponse.json({ response });

  } catch (error) {
    console.error('Error in AI chat:', error);
    
    // Return fallback response if OpenAI fails
    const fallbackResponse = `I understand you'd like to improve the document with: "${message}". 

While I'm currently unable to process your request with AI, here are some general suggestions:

- For adding more details: Consider expanding on key sections with specific examples and data
- For making it more professional: Use formal language, clear headings, and structured formatting
- For improving clarity: Break down complex ideas into bullet points and shorter paragraphs
- For better organization: Ensure logical flow from introduction to conclusion

Please try again later when AI services are available, or make these improvements manually.`;

    return NextResponse.json({ 
      response: fallbackResponse,
      fallback: true,
      error: 'OpenAI unavailable, using fallback response'
    });
  }
}
