-- Simple Test Data for KaiNote
-- Run this AFTER the migration script to add sample data
-- This script works with existing users and creates minimal test data

-- First, let's see what users exist and create a test project for the first user
DO $$
DECLARE
    test_user_id UUID;
    test_project_id UUID := '550e8400-e29b-41d4-a716-************';
BEGIN
    -- Get the first user ID from existing users
    SELECT id INTO test_user_id FROM public.users LIMIT 1;
    
    -- If no users exist, create a demo user that works with auth
    IF test_user_id IS NULL THEN
        -- Insert a demo user (this assumes you have auth.users set up)
        INSERT INTO public.users (
            id, 
            email, 
            name, 
            subscription_tier, 
            hourly_rate,
            created_at, 
            updated_at
        ) VALUES (
            '550e8400-e29b-41d4-a716-************',
            '<EMAIL>',
            'Demo User',
            'pro',
            75.00,
            NOW(),
            NOW()
        ) ON CONFLICT (id) DO NOTHING;
        
        test_user_id := '550e8400-e29b-41d4-a716-************';
    END IF;
    
    -- Create a test project
    INSERT INTO public.projects (
        id,
        user_id,
        name,
        client_name,
        client_email,
        description,
        status,
        budget,
        deadline,
        created_at,
        updated_at
    ) VALUES (
        test_project_id,
        test_user_id,
        'E-commerce Website Redesign',
        'TechCorp Solutions',
        '<EMAIL>',
        'Complete redesign of the company e-commerce platform with modern UI/UX, improved performance, and mobile responsiveness.',
        'active',
        15000.00,
        NOW() + INTERVAL '30 days',
        NOW(),
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        name = EXCLUDED.name,
        client_name = EXCLUDED.client_name,
        client_email = EXCLUDED.client_email,
        description = EXCLUDED.description,
        updated_at = NOW();
    
    -- First, add missing columns to project_tasks table if they don't exist
    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'project_tasks' AND column_name = 'user_id') THEN
        ALTER TABLE public.project_tasks ADD COLUMN user_id UUID REFERENCES public.users(id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'project_tasks' AND column_name = 'assigned_to') THEN
        ALTER TABLE public.project_tasks ADD COLUMN assigned_to UUID REFERENCES public.users(id);
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'project_tasks' AND column_name = 'due_date') THEN
        ALTER TABLE public.project_tasks ADD COLUMN due_date TIMESTAMP WITH TIME ZONE;
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'project_tasks' AND column_name = 'created_at') THEN
        ALTER TABLE public.project_tasks ADD COLUMN created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;

    IF NOT EXISTS (SELECT 1 FROM information_schema.columns
                   WHERE table_name = 'project_tasks' AND column_name = 'updated_at') THEN
        ALTER TABLE public.project_tasks ADD COLUMN updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW();
    END IF;

    -- Create sample tasks
    INSERT INTO public.project_tasks (
        id,
        project_id,
        user_id,
        title,
        description,
        status,
        priority,
        assigned_to,
        due_date,
        created_at,
        updated_at
    ) VALUES
    (
        '550e8400-e29b-41d4-a716-************',
        test_project_id,
        test_user_id,
        'Design System Creation',
        'Create a comprehensive design system with components, colors, and typography guidelines.',
        'in_progress',
        'high',
        test_user_id,
        NOW() + INTERVAL '7 days',
        NOW(),
        NOW()
    ),
    (
        '550e8400-e29b-41d4-a716-************',
        test_project_id,
        test_user_id,
        'Homepage Wireframes',
        'Create detailed wireframes for the new homepage layout and user flow.',
        'completed',
        'medium',
        test_user_id,
        NOW() - INTERVAL '1 day',
        NOW() - INTERVAL '5 days',
        NOW() - INTERVAL '1 day'
    ),
    (
        '550e8400-e29b-41d4-a716-************',
        test_project_id,
        test_user_id,
        'Performance Optimization',
        'Optimize website loading speed and implement caching strategies.',
        'todo',
        'medium',
        test_user_id,
        NOW() + INTERVAL '14 days',
        NOW(),
        NOW()
    ) ON CONFLICT (id) DO UPDATE SET
        title = EXCLUDED.title,
        description = EXCLUDED.description,
        status = EXCLUDED.status,
        updated_at = NOW();
    
    -- Create sample meeting
    INSERT INTO public.meetings (
        id,
        user_id,
        project_id,
        title,
        platform,
        duration_minutes,
        recorded_at,
        transcription_status,
        created_at,
        updated_at
    ) VALUES (
        '550e8400-e29b-41d4-a716-446655440020',
        test_user_id,
        test_project_id,
        'Project Kickoff Meeting',
        'zoom',
        60,
        NOW() - INTERVAL '7 days',
        'completed',
        NOW() - INTERVAL '7 days',
        NOW() - INTERVAL '7 days'
    ) ON CONFLICT (id) DO UPDATE SET
        title = EXCLUDED.title,
        updated_at = NOW();
    
    -- Create sample invoice
    INSERT INTO public.invoices (
        id,
        project_id,
        user_id,
        invoice_number,
        amount,
        currency,
        status,
        due_date,
        created_at,
        updated_at
    ) VALUES (
        '550e8400-e29b-41d4-a716-446655440030',
        test_project_id,
        test_user_id,
        'INV-2024-001',
        5000.00,
        'USD',
        'sent',
        NOW() + INTERVAL '30 days',
        NOW() - INTERVAL '5 days',
        NOW() - INTERVAL '5 days'
    ) ON CONFLICT (id) DO UPDATE SET
        amount = EXCLUDED.amount,
        status = EXCLUDED.status,
        updated_at = NOW();
    
    RAISE NOTICE 'Test data created successfully for user: %', test_user_id;
    RAISE NOTICE 'Test project ID: %', test_project_id;
    
END $$;

-- Display success message
SELECT 
    'Test data created successfully!' AS message,
    'Visit: http://localhost:3003/projects/550e8400-e29b-41d4-a716-************' AS test_url;
