// Unit tests for validation utilities
describe('Validation Utils', () => {
  describe('Email Validation', () => {
    const validateEmail = (email: string): boolean => {
      const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      return emailRegex.test(email);
    };

    it('should validate correct email formats', () => {
      const validEmails = [
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>',
        '<EMAIL>'
      ];

      validEmails.forEach(email => {
        expect(validateEmail(email)).toBe(true);
      });
    });

    it('should reject invalid email formats', () => {
      const invalidEmails = [
        'invalid-email',
        '@example.com',
        'user@',
        'user@.com',
        'user@example',
        ''
      ];

      invalidEmails.forEach(email => {
        expect(validateEmail(email)).toBe(false);
      });
    });
  });

  describe('Password Validation', () => {
    const validatePassword = (password: string): { isValid: boolean; errors: string[] } => {
      const errors: string[] = [];
      
      if (password.length < 8) {
        errors.push('Password must be at least 8 characters long');
      }
      
      if (!/[A-Z]/.test(password)) {
        errors.push('Password must contain at least one uppercase letter');
      }
      
      if (!/[a-z]/.test(password)) {
        errors.push('Password must contain at least one lowercase letter');
      }
      
      if (!/\d/.test(password)) {
        errors.push('Password must contain at least one number');
      }

      return {
        isValid: errors.length === 0,
        errors
      };
    };

    it('should validate strong passwords', () => {
      const strongPasswords = [
        'Password123',
        'MySecure1Pass',
        'Test1234',
        'Complex9Password'
      ];

      strongPasswords.forEach(password => {
        const result = validatePassword(password);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('should reject weak passwords', () => {
      const weakPasswords = [
        { password: 'short', expectedErrors: 3 }, // too short, no uppercase, no number
        { password: 'alllowercase', expectedErrors: 2 }, // no uppercase, no number
        { password: 'ALLUPPERCASE', expectedErrors: 2 }, // no lowercase, no number
        { password: 'NoNumbers', expectedErrors: 1 }, // no numbers
        { password: '12345678', expectedErrors: 2 } // no letters
      ];

      weakPasswords.forEach(({ password, expectedErrors }) => {
        const result = validatePassword(password);
        expect(result.isValid).toBe(false);
        expect(result.errors.length).toBeGreaterThanOrEqual(expectedErrors);
      });
    });
  });

  describe('Input Sanitization', () => {
    const sanitizeInput = (input: string): string => {
      return input
        .trim()
        .replace(/<[^>]*>/g, '') // Remove HTML tags
        .substring(0, 1000); // Limit length
    };

    it('should trim whitespace', () => {
      expect(sanitizeInput('  test  ')).toBe('test');
      expect(sanitizeInput('\n\ttest\n\t')).toBe('test');
    });

    it('should remove HTML tags', () => {
      expect(sanitizeInput('test<script>alert("xss")</script>')).toBe('testalert("xss")');
      expect(sanitizeInput('<div>content</div>')).toBe('content');
    });

    it('should limit input length', () => {
      const longInput = 'a'.repeat(2000);
      const sanitized = sanitizeInput(longInput);
      expect(sanitized.length).toBe(1000);
    });
  });

  describe('Date Validation', () => {
    const validateDate = (dateString: string): boolean => {
      const date = new Date(dateString);
      return date instanceof Date && !isNaN(date.getTime());
    };

    const isValidDateRange = (startDate: string, endDate: string): boolean => {
      const start = new Date(startDate);
      const end = new Date(endDate);
      return validateDate(startDate) && validateDate(endDate) && start <= end;
    };

    it('should validate correct date formats', () => {
      const validDates = [
        '2024-01-15',
        '2024-12-31T23:59:59Z',
        '2024-06-15T10:30:00.000Z'
      ];

      validDates.forEach(date => {
        expect(validateDate(date)).toBe(true);
      });
    });

    it('should reject invalid dates', () => {
      const invalidDates = [
        'invalid-date',
        '2024-13-01', // Invalid month
        ''
      ];

      invalidDates.forEach(date => {
        expect(validateDate(date)).toBe(false);
      });
    });

    it('should validate date ranges', () => {
      expect(isValidDateRange('2024-01-01', '2024-01-31')).toBe(true);
      expect(isValidDateRange('2024-01-31', '2024-01-01')).toBe(false); // End before start
      expect(isValidDateRange('invalid', '2024-01-01')).toBe(false); // Invalid start
    });
  });

  describe('Numeric Validation', () => {
    const validatePositiveNumber = (value: any): boolean => {
      const num = Number(value);
      return !isNaN(num) && num > 0;
    };

    const validateCurrency = (value: any): boolean => {
      const num = Number(value);
      return !isNaN(num) && num >= 0 && Number.isFinite(num);
    };

    it('should validate positive numbers', () => {
      expect(validatePositiveNumber(1)).toBe(true);
      expect(validatePositiveNumber('5.5')).toBe(true);
      expect(validatePositiveNumber(100)).toBe(true);
      
      expect(validatePositiveNumber(0)).toBe(false);
      expect(validatePositiveNumber(-1)).toBe(false);
      expect(validatePositiveNumber('invalid')).toBe(false);
      expect(validatePositiveNumber(null)).toBe(false);
    });

    it('should validate currency values', () => {
      expect(validateCurrency(0)).toBe(true);
      expect(validateCurrency(99.99)).toBe(true);
      expect(validateCurrency('150.50')).toBe(true);
      
      expect(validateCurrency(-1)).toBe(false);
      expect(validateCurrency('invalid')).toBe(false);
      expect(validateCurrency(Infinity)).toBe(false);
    });
  });

  describe('Project Validation', () => {
    interface ProjectData {
      name?: string;
      description?: string;
      hourly_rate?: number;
      status?: string;
    }

    const validateProject = (data: ProjectData): { isValid: boolean; errors: string[] } => {
      const errors: string[] = [];

      if (!data.name || data.name.trim().length === 0) {
        errors.push('Project name is required');
      }

      if (data.name && data.name.length > 100) {
        errors.push('Project name must be less than 100 characters');
      }

      if (data.hourly_rate !== undefined && (isNaN(data.hourly_rate) || data.hourly_rate < 0)) {
        errors.push('Hourly rate must be a positive number');
      }

      if (data.status && !['active', 'completed', 'on_hold', 'cancelled'].includes(data.status)) {
        errors.push('Invalid project status');
      }

      return {
        isValid: errors.length === 0,
        errors
      };
    };

    it('should validate correct project data', () => {
      const validProjects = [
        {
          name: 'Test Project',
          description: 'A test project',
          hourly_rate: 75.00,
          status: 'active'
        },
        {
          name: 'Another Project',
          hourly_rate: 100
        }
      ];

      validProjects.forEach(project => {
        const result = validateProject(project);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('should reject invalid project data', () => {
      const invalidProjects = [
        { data: {}, expectedErrors: ['Project name is required'] },
        { data: { name: '' }, expectedErrors: ['Project name is required'] },
        { data: { name: 'a'.repeat(101) }, expectedErrors: ['Project name must be less than 100 characters'] },
        { data: { name: 'Test', hourly_rate: -50 }, expectedErrors: ['Hourly rate must be a positive number'] },
        { data: { name: 'Test', status: 'invalid' }, expectedErrors: ['Invalid project status'] }
      ];

      invalidProjects.forEach(({ data, expectedErrors }) => {
        const result = validateProject(data);
        expect(result.isValid).toBe(false);
        expectedErrors.forEach(error => {
          expect(result.errors).toContain(error);
        });
      });
    });
  });
});
