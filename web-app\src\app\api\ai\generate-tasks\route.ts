import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const { projectDescription, additionalContext, hasUploadedFiles, uploadedFileInfo } = await request.json();

    if (!projectDescription) {
      return NextResponse.json(
        { error: 'Project description is required' },
        { status: 400 }
      );
    }

    // Check if OpenAI API key is configured
    if (!process.env.OPENAI_API_KEY || process.env.OPENAI_API_KEY === 'your_openai_api_key_here') {
      console.log('OpenAI API key not configured');
      return NextResponse.json(
        {
          tasks: [],
          error: 'AI service unavailable',
          message: 'AI task generation is currently unavailable. Please configure OpenAI API key or create tasks manually.',
          details: 'OpenAI API key not configured in environment variables'
        },
        { status: 503 }
      );
    }

    console.log('AI Task Generation Request:', {
      hasText: !!projectDescription.trim(),
      textLength: projectDescription.length,
      hasUploadedFiles,
      fileCount: uploadedFileInfo?.length || 0,
      files: uploadedFileInfo?.map((f: any) => ({ name: f.name, type: f.type })) || []
    });

    const prompt = `
You are an expert project manager. Analyze the following project information and ONLY generate tasks that are directly mentioned or clearly implied in the provided description.

Project Description: ${projectDescription}
${additionalContext ? `Additional Context: ${additionalContext}` : ''}

IMPORTANT RULES:
1. ONLY create tasks that are explicitly mentioned or directly implied in the project description
2. DO NOT add generic development tasks unless specifically mentioned
3. If the project description is too vague or doesn't contain enough information to identify specific tasks, respond with an empty tasks array
4. Base tasks ONLY on what is actually described in the project information
5. Do not assume standard project phases unless they are mentioned

If you can identify specific tasks from the description, format them as:
{
  "tasks": [
    {
      "title": "Task Title (based on description)",
      "description": "Detailed description based on what was mentioned",
      "priority": "high|medium|low",
      "estimatedDays": number,
      "assigneeRole": "Role Name"
    }
  ]
}

If the description is too vague or doesn't contain enough specific information to identify tasks, respond with:
{
  "tasks": [],
  "message": "No specific tasks could be identified from the provided project description. Please provide more detailed information about what needs to be accomplished."
}
`;

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: "You are an expert project manager who ONLY creates tasks based on explicitly provided information. Never add generic or assumed tasks. If insufficient information is provided, return an empty tasks array. Always respond with valid JSON."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 1500
    });

    const response = completion.choices[0]?.message?.content;
    if (!response) {
      throw new Error('No response from OpenAI');
    }

    // Parse the JSON response
    const parsedResponse = JSON.parse(response);

    // Return the response (could be tasks or empty array with message)
    return NextResponse.json({
      tasks: parsedResponse.tasks || [],
      message: parsedResponse.message || null
    });

  } catch (error) {
    console.error('Error generating tasks with AI:', error);

    // Return proper error message instead of fallback
    return NextResponse.json({
      tasks: [],
      error: 'AI processing failed',
      message: 'AI task generation is currently experiencing issues. Please try again later or create tasks manually.',
      details: error instanceof Error ? error.message : 'Unknown AI processing error'
    }, { status: 503 });
  }
}
