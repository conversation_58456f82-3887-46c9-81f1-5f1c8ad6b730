'use client';

import { useState, useEffect } from 'react';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import apiHelpers from '@/lib/api';
import { 
  PlayIcon, 
  StopIcon, 
  ClockIcon,
  ChevronDownIcon
} from '@heroicons/react/24/outline';
import { Listbox, Transition } from '@headlessui/react';
import { Fragment } from 'react';

interface Project {
  id: string;
  name: string;
  client_name: string;
}

interface ActiveTimer {
  id: string;
  project_id: string;
  description?: string;
  start_time: string;
  project?: Project;
}

interface TimerWidgetProps {
  className?: string;
}

export function TimerWidget({ className = '' }: TimerWidgetProps) {
  const queryClient = useQueryClient();
  const [selectedProject, setSelectedProject] = useState<Project | null>(null);
  const [description, setDescription] = useState('');
  const [isExpanded, setIsExpanded] = useState(false);

  // Fetch active timer
  const { data: activeTimer, refetch: refetchActiveTimer } = useQuery<{ data: ActiveTimer | null }>(
    'active-timer',
    apiHelpers.getActiveTimer,
    {
      refetchInterval: 1000, // Update every second
    }
  );

  // Fetch projects
  const { data: projectsResponse } = useQuery(
    'projects',
    apiHelpers.getProjects,
    {
      select: (response) => response.data.data,
    }
  );

  // Start timer mutation
  const startTimerMutation = useMutation(
    (data: { project_id: string; description?: string }) => apiHelpers.startTimer(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('active-timer');
        setDescription('');
        setIsExpanded(false);
      },
    }
  );

  // Stop timer mutation
  const stopTimerMutation = useMutation(
    (data: { description?: string }) => apiHelpers.stopTimer(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('active-timer');
        queryClient.invalidateQueries('time-entries');
        queryClient.invalidateQueries('time-analytics');
      },
    }
  );

  const projects = Array.isArray(projectsResponse) ? projectsResponse : [];
  const timer = activeTimer?.data;

  // Calculate elapsed time
  const getElapsedTime = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  const handleStartTimer = async () => {
    if (!selectedProject) return;
    
    try {
      await startTimerMutation.mutateAsync({
        project_id: selectedProject.id,
        description: description || undefined,
      });
    } catch (error) {
      console.error('Error starting timer:', error);
    }
  };

  const handleStopTimer = async () => {
    try {
      await stopTimerMutation.mutateAsync({
        description: description || undefined,
      });
    } catch (error) {
      console.error('Error stopping timer:', error);
    }
  };

  // Set default project if none selected
  useEffect(() => {
    if (!selectedProject && Array.isArray(projects) && projects.length > 0) {
      setSelectedProject(projects[0]);
    }
  }, [projects, selectedProject]);

  return (
    <div className={`bg-white rounded-lg shadow-lg border border-gray-200 ${className}`}>
      {timer ? (
        // Active Timer Display
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="bg-green-100 rounded-full p-2">
                <ClockIcon className="h-5 w-5 text-green-600" />
              </div>
              <div>
                <h3 className="text-lg font-semibold text-gray-900">Timer Running</h3>
                <p className="text-sm text-gray-600">
                  {timer.project?.name} - {timer.project?.client_name}
                </p>
              </div>
            </div>
            <div className="text-right">
              <div className="text-2xl font-mono font-bold text-gray-900">
                {getElapsedTime(timer.start_time)}
              </div>
            </div>
          </div>

          {timer.description && (
            <p className="text-sm text-gray-600 mb-4">{timer.description}</p>
          )}

          <div className="flex items-center space-x-3">
            <input
              type="text"
              placeholder="Add description (optional)"
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              className="flex-1 rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-sm"
            />
            <button
              onClick={handleStopTimer}
              disabled={stopTimerMutation.isLoading}
              className="bg-red-600 text-white px-4 py-2 rounded-lg hover:bg-red-700 disabled:opacity-50 flex items-center space-x-2"
            >
              <StopIcon className="h-4 w-4" />
              <span>Stop</span>
            </button>
          </div>
        </div>
      ) : (
        // Start Timer Form
        <div className="p-6">
          <div className="flex items-center justify-between mb-4">
            <div className="flex items-center space-x-3">
              <div className="bg-blue-100 rounded-full p-2">
                <ClockIcon className="h-5 w-5 text-blue-600" />
              </div>
              <h3 className="text-lg font-semibold text-gray-900">Time Tracker</h3>
            </div>
            <button
              onClick={() => setIsExpanded(!isExpanded)}
              className="text-gray-400 hover:text-gray-600"
            >
              <ChevronDownIcon className={`h-5 w-5 transform transition-transform ${isExpanded ? 'rotate-180' : ''}`} />
            </button>
          </div>

          {isExpanded && (
            <div className="space-y-4">
              {/* Project Selection */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Project
                </label>
                <Listbox value={selectedProject} onChange={setSelectedProject}>
                  <div className="relative">
                    <Listbox.Button className="relative w-full cursor-default rounded-md border border-gray-300 bg-white py-2 pl-3 pr-10 text-left shadow-sm focus:border-primary-500 focus:outline-none focus:ring-1 focus:ring-primary-500 text-sm">
                      <span className="block truncate">
                        {selectedProject ? `${selectedProject.name} - ${selectedProject.client_name}` : 'Select a project'}
                      </span>
                      <span className="pointer-events-none absolute inset-y-0 right-0 flex items-center pr-2">
                        <ChevronDownIcon className="h-5 w-5 text-gray-400" aria-hidden="true" />
                      </span>
                    </Listbox.Button>
                    <Transition
                      as={Fragment}
                      leave="transition ease-in duration-100"
                      leaveFrom="opacity-100"
                      leaveTo="opacity-0"
                    >
                      <Listbox.Options className="absolute z-10 mt-1 max-h-60 w-full overflow-auto rounded-md bg-white py-1 text-base shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none text-sm">
                        {projects && projects.length > 0 ? projects.map((project: Project) => (
                          <Listbox.Option
                            key={project.id}
                            className={({ active }) =>
                              `relative cursor-default select-none py-2 pl-3 pr-9 ${
                                active ? 'bg-primary-600 text-white' : 'text-gray-900'
                              }`
                            }
                            value={project}
                          >
                            {({ selected, active }) => (
                              <>
                                <span className={`block truncate ${selected ? 'font-medium' : 'font-normal'}`}>
                                  {project.name} - {project.client_name}
                                </span>
                              </>
                            )}
                          </Listbox.Option>
                        )) : (
                          <div className="py-2 px-3 text-gray-500 text-sm">
                            No projects available. Create a project first.
                          </div>
                        )}
                      </Listbox.Options>
                    </Transition>
                  </div>
                </Listbox>
              </div>

              {/* Description */}
              <div>
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Description (optional)
                </label>
                <input
                  type="text"
                  placeholder="What are you working on?"
                  value={description}
                  onChange={(e) => setDescription(e.target.value)}
                  className="w-full rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500 text-sm"
                />
              </div>
            </div>
          )}

          {/* Start Button */}
          <div className="mt-4">
            <button
              onClick={handleStartTimer}
              disabled={!selectedProject || startTimerMutation.isLoading}
              className="w-full bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center space-x-2"
            >
              <PlayIcon className="h-4 w-4" />
              <span>{startTimerMutation.isLoading ? 'Starting...' : 'Start Timer'}</span>
            </button>
          </div>

          {!isExpanded && selectedProject && (
            <p className="mt-2 text-xs text-gray-500 text-center">
              Quick start: {selectedProject.name}
            </p>
          )}
        </div>
      )}
    </div>
  );
}
