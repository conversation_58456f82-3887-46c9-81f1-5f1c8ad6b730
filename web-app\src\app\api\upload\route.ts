import { NextRequest, NextResponse } from 'next/server';
import { writeFile, mkdir } from 'fs/promises';
import { join } from 'path';
import { existsSync } from 'fs';

export async function POST(request: NextRequest) {
  try {
    const formData = await request.formData();
    const file = formData.get('file') as File;
    const projectId = formData.get('projectId') as string;
    const uploadType = formData.get('uploadType') as string || 'document'; // document, audio, transcript

    if (!file) {
      return NextResponse.json(
        { success: false, error: 'No file provided' },
        { status: 400 }
      );
    }

    // Create uploads directory if it doesn't exist
    const uploadsDir = join(process.cwd(), 'public', 'uploads');
    if (!existsSync(uploadsDir)) {
      await mkdir(uploadsDir, { recursive: true });
    }

    // Create project-specific directory
    const projectDir = join(uploadsDir, projectId || 'general');
    if (!existsSync(projectDir)) {
      await mkdir(projectDir, { recursive: true });
    }

    // Create type-specific directory
    const typeDir = join(projectDir, uploadType);
    if (!existsSync(typeDir)) {
      await mkdir(typeDir, { recursive: true });
    }

    // Generate unique filename
    const timestamp = Date.now();
    const originalName = file.name;
    const extension = originalName.split('.').pop();
    const filename = `${timestamp}-${originalName.replace(/[^a-zA-Z0-9.-]/g, '_')}`;
    const filepath = join(typeDir, filename);

    // Convert file to buffer and save
    const bytes = await file.arrayBuffer();
    const buffer = Buffer.from(bytes);
    await writeFile(filepath, buffer);

    // Generate public URL
    const publicUrl = `/uploads/${projectId || 'general'}/${uploadType}/${filename}`;

    console.log('File uploaded successfully:', {
      originalName,
      filename,
      size: file.size,
      type: file.type,
      publicUrl
    });

    return NextResponse.json({
      success: true,
      data: {
        id: timestamp.toString(),
        name: originalName,
        filename,
        file_url: publicUrl,
        file_type: extension,
        file_size: file.size,
        mime_type: file.type,
        upload_type: uploadType,
        created_at: new Date().toISOString()
      }
    });

  } catch (error) {
    console.error('File upload error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'File upload failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}

// Handle file downloads
export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const filePath = searchParams.get('path');

    if (!filePath) {
      return NextResponse.json(
        { success: false, error: 'File path required' },
        { status: 400 }
      );
    }

    // Security check - ensure path is within uploads directory
    if (!filePath.startsWith('/uploads/')) {
      return NextResponse.json(
        { success: false, error: 'Invalid file path' },
        { status: 403 }
      );
    }

    const fullPath = join(process.cwd(), 'public', filePath);
    
    if (!existsSync(fullPath)) {
      return NextResponse.json(
        { success: false, error: 'File not found' },
        { status: 404 }
      );
    }

    // Return file info for download
    return NextResponse.json({
      success: true,
      data: {
        download_url: filePath,
        exists: true
      }
    });

  } catch (error) {
    console.error('File download error:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'File download failed',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
