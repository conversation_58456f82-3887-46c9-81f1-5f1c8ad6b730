-- KaiNote Test Data Script
-- Run this in your Supabase SQL Editor to create sample data for testing

-- First, let's create a test user in auth.users (this simulates account creation)
-- Note: In production, this would be done through Supabase Auth signup
INSERT INTO auth.users (
  id,
  instance_id,
  email,
  encrypted_password,
  email_confirmed_at,
  created_at,
  updated_at,
  raw_app_meta_data,
  raw_user_meta_data,
  is_super_admin,
  role
) VALUES (
  '550e8400-e29b-41d4-a716-************',
  '********-0000-0000-0000-********0000',
  '<EMAIL>',
  '$2a$10$dummy.encrypted.password.hash.for.testing.purposes.only',
  NOW(),
  NOW(),
  NOW(),
  '{"provider": "email", "providers": ["email"]}',
  '{"name": "Demo User"}',
  false,
  'authenticated'
) ON CONFLICT (id) DO NOTHING;

-- Create corresponding user in public.users table
INSERT INTO public.users (
  id,
  email,
  name,
  subscription_tier,
  hourly_rate,
  created_at,
  updated_at
) VALUES (
  '550e8400-e29b-41d4-a716-************',
  '<EMAIL>',
  'Demo User',
  'pro',
  75.00,
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  email = EXCLUDED.email,
  name = EXCLUDED.name,
  subscription_tier = EXCLUDED.subscription_tier,
  hourly_rate = EXCLUDED.hourly_rate,
  updated_at = NOW();

-- Create test projects
INSERT INTO public.projects (
  id,
  user_id,
  name,
  client_name,
  client_email,
  description,
  status,
  budget,
  deadline,
  created_at,
  updated_at
) VALUES 
(
  '550e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  'E-commerce Website Redesign',
  'TechCorp Solutions',
  '<EMAIL>',
  'Complete redesign of the company e-commerce platform with modern UI/UX, improved performance, and mobile responsiveness.',
  'active',
  15000.00,
  NOW() + INTERVAL '30 days',
  NOW(),
  NOW()
),
(
  '550e8400-e29b-41d4-a716-446655440002',
  '550e8400-e29b-41d4-a716-************',
  'Mobile App Development',
  'RetailMax Inc',
  '<EMAIL>',
  'Native iOS and Android app for customer engagement and loyalty program management.',
  'planning',
  25000.00,
  NOW() + INTERVAL '60 days',
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  client_name = EXCLUDED.client_name,
  client_email = EXCLUDED.client_email,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  budget = EXCLUDED.budget,
  deadline = EXCLUDED.deadline,
  updated_at = NOW();

-- Create test tasks
INSERT INTO public.project_tasks (
  id,
  project_id,
  user_id,
  title,
  description,
  status,
  priority,
  assigned_to,
  due_date,
  completed_at,
  created_at,
  updated_at
) VALUES 
(
  '550e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  'Design System Creation',
  'Create a comprehensive design system with components, colors, and typography guidelines.',
  'in_progress',
  'high',
  '550e8400-e29b-41d4-a716-************',
  NOW() + INTERVAL '7 days',
  NULL,
  NOW(),
  NOW()
),
(
  '550e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  'Homepage Wireframes',
  'Create detailed wireframes for the new homepage layout and user flow.',
  'completed',
  'medium',
  '550e8400-e29b-41d4-a716-************',
  NOW() - INTERVAL '1 day',
  NOW() - INTERVAL '1 day',
  NOW() - INTERVAL '5 days',
  NOW() - INTERVAL '1 day'
),
(
  '550e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  'Performance Optimization',
  'Optimize website loading speed and implement caching strategies.',
  'todo',
  'medium',
  '550e8400-e29b-41d4-a716-************',
  NOW() + INTERVAL '14 days',
  NULL,
  NOW(),
  NOW()
) ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  description = EXCLUDED.description,
  status = EXCLUDED.status,
  priority = EXCLUDED.priority,
  due_date = EXCLUDED.due_date,
  updated_at = NOW();

-- Create test meetings
INSERT INTO public.meetings (
  id,
  user_id,
  project_id,
  title,
  platform,
  duration_minutes,
  recorded_at,
  transcription_status,
  audio_url,
  meeting_url,
  created_at,
  updated_at
) VALUES 
(
  '550e8400-e29b-41d4-a716-446655440020',
  '550e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  'Project Kickoff Meeting',
  'zoom',
  60,
  NOW() - INTERVAL '7 days',
  'completed',
  'https://example.com/audio/meeting1.mp3',
  'https://zoom.us/j/123456789',
  NOW() - INTERVAL '7 days',
  NOW() - INTERVAL '7 days'
),
(
  '550e8400-e29b-41d4-a716-446655440021',
  '550e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  'Design Review Session',
  'google-meet',
  45,
  NOW() - INTERVAL '3 days',
  'completed',
  'https://example.com/audio/meeting2.mp3',
  'https://meet.google.com/abc-defg-hij',
  NOW() - INTERVAL '3 days',
  NOW() - INTERVAL '3 days'
) ON CONFLICT (id) DO UPDATE SET
  title = EXCLUDED.title,
  platform = EXCLUDED.platform,
  duration_minutes = EXCLUDED.duration_minutes,
  recorded_at = EXCLUDED.recorded_at,
  updated_at = NOW();

-- Create test invoices
INSERT INTO public.invoices (
  id,
  project_id,
  user_id,
  invoice_number,
  amount,
  currency,
  status,
  due_date,
  sent_at,
  paid_at,
  notes,
  created_at,
  updated_at
) VALUES 
(
  '550e8400-e29b-41d4-a716-446655440030',
  '550e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  'INV-2024-001',
  5000.00,
  'USD',
  'paid',
  NOW() + INTERVAL '30 days',
  NOW() - INTERVAL '10 days',
  NOW() - INTERVAL '2 days',
  'Initial project milestone payment',
  NOW() - INTERVAL '15 days',
  NOW() - INTERVAL '2 days'
),
(
  '550e8400-e29b-41d4-a716-446655440031',
  '550e8400-e29b-41d4-a716-************',
  '550e8400-e29b-41d4-a716-************',
  'INV-2024-002',
  7500.00,
  'USD',
  'sent',
  NOW() + INTERVAL '30 days',
  NOW() - INTERVAL '5 days',
  NULL,
  'Second milestone payment',
  NOW() - INTERVAL '5 days',
  NOW() - INTERVAL '5 days'
) ON CONFLICT (id) DO UPDATE SET
  amount = EXCLUDED.amount,
  status = EXCLUDED.status,
  due_date = EXCLUDED.due_date,
  updated_at = NOW();

-- Create test documents
INSERT INTO public.project_documents (
  id,
  project_id,
  name,
  file_url,
  file_type,
  file_size,
  uploaded_by,
  created_at
) VALUES 
(
  '550e8400-e29b-41d4-a716-446655440040',
  '550e8400-e29b-41d4-a716-************',
  'Project Requirements.pdf',
  'https://example.com/docs/requirements.pdf',
  'application/pdf',
  2048576,
  '550e8400-e29b-41d4-a716-************',
  NOW() - INTERVAL '10 days'
),
(
  '550e8400-e29b-41d4-a716-446655440041',
  '550e8400-e29b-41d4-a716-************',
  'Design Mockups.figma',
  'https://figma.com/file/abc123',
  'application/figma',
  5242880,
  '550e8400-e29b-41d4-a716-************',
  NOW() - INTERVAL '5 days'
) ON CONFLICT (id) DO UPDATE SET
  name = EXCLUDED.name,
  file_url = EXCLUDED.file_url,
  file_type = EXCLUDED.file_type;

-- Success message
SELECT 'Test data created successfully! You can now test with project ID: 550e8400-e29b-41d4-a716-************' AS message;
