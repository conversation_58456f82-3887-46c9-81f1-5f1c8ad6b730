# KaiNote for Freelancers

Transform virtual client meetings into actionable results by recording, transcribing, extracting freelancer-specific action items, and delivering smart reminders.

## 🎯 Key Features

- **Smart Meeting Recording**: Chrome extension for browser-based meetings
- **AI Transcription**: Whisper API with speaker identification
- **Action Item Extraction**: GPT-4 powered freelancer-specific task extraction
- **Cost Calculator**: Meeting value assessment
- **Smart Reminders**: Automated deadline and task notifications
- **Client Summaries**: Professional meeting summaries for clients
- **Task Management**: Lightweight to-do interface

## 🏗️ Architecture

```
kainote/
├── chrome-extension/     # Chrome extension for meeting recording
├── web-app/             # Next.js dashboard and task management
├── api/                 # Node.js backend services
├── shared/              # Shared types and utilities
└── docs/                # Documentation
```

## 🚀 Tech Stack

- **Frontend**: Next.js 14, React, TypeScript, Tailwind CSS
- **Backend**: Node.js, Express, TypeScript
- **Database**: PostgreSQL (Supabase)
- **AI Services**: OpenAI GPT-4, Whisper API
- **Authentication**: Supabase Auth
- **Deployment**: Vercel (web), Railway (api)

## 💰 Pricing

- **Free**: 3 meetings/month, basic features
- **Pro**: $9.99/month, 300 minutes/month, advanced features

## 🛠️ Development Setup

### Prerequisites
- Node.js 18+ and npm
- Python 3.9+ (for API server)

### Quick Start

1. **Clone the repository**
   ```bash
   git clone <repository-url>
   cd kainote
   ```

2. **Start the API server**
   ```bash
   cd api
   npm install
   npm run dev
   ```
   API will be available at `http://localhost:3001`

3. **Start the web application**
   ```bash
   cd web-app
   npm install
   npm run dev
   ```
   Web app will be available at `http://localhost:3000`

4. **Demo Mode**
   - The application runs in demo mode by default
   - Use any email/password to sign in (e.g., `<EMAIL>` / `demo123`)
   - All features are available with mock data

### Current Status ✅
- **Landing Page**: Professional homepage with features and pricing
- **Authentication**: Demo mode with any credentials
- **Dashboard**: Complete freelancer dashboard with widgets
- **Time Tracking**: Timer widget and analytics
- **Expense Management**: Quick expense entry
- **Meeting Management**: Upload and transcription features
- **Project Management**: Client and project tracking
- **Financial Analytics**: Revenue and profit insights

## 📝 License

MIT License - see LICENSE file for details
