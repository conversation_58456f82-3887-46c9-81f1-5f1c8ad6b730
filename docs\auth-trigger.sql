-- Auto-create public.users record when someone signs up
-- This trigger ensures that when a user creates an account through Supabase Auth,
-- a corresponding record is automatically created in the public.users table

-- Function to handle new user creation
CREATE OR REPLACE FUNCTION public.handle_new_user()
RETURNS TRIGGER AS $$
BEGIN
  INSERT INTO public.users (id, email, name, subscription_tier, hourly_rate, created_at, updated_at)
  VALUES (
    NEW.id,
    NEW.email,
    COALESCE(NEW.raw_user_meta_data->>'name', NEW.email),
    'free',
    75.00,
    NOW(),
    NOW()
  );
  RETURN NEW;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create trigger on auth.users table
DROP TRIGGER IF EXISTS on_auth_user_created ON auth.users;
CREATE TRIGGER on_auth_user_created
  AFTER INSERT ON auth.users
  FOR EACH ROW EXECUTE FUNCTION public.handle_new_user();

-- Grant necessary permissions
GRANT USAGE ON SCHEMA public TO anon, authenticated;
GRANT ALL ON public.users TO anon, authenticated;

-- Test the trigger (optional - remove this in production)
-- This will verify that the trigger works correctly
DO $$
BEGIN
  RAISE NOTICE 'Auth trigger created successfully!';
  RAISE NOTICE 'When users sign up through Supabase Auth, they will automatically get a record in public.users';
END $$;
