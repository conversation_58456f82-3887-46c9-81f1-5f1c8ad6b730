'use client';

import { useEffect, useState } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery } from 'react-query';
import { useAuth } from '@/lib/auth';
import { apiHelpers } from '@/lib/api';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { TimerWidget } from '@/components/time-tracking/TimerWidget';
import { ExpenseWidget } from '@/components/expenses/ExpenseWidget';
import { MeetingBotWidget } from '@/components/meeting-bot/MeetingBotWidget';
import {
  CheckCircleIcon,
  ClockIcon,
  ExclamationTriangleIcon,
  MicrophoneIcon,
  PlusIcon,
  FolderIcon,
  UserGroupIcon,
} from '@heroicons/react/24/outline';
import Link from 'next/link';
import { formatDate } from '@/lib/utils';
import { api } from '@/lib/api';

export default function DashboardPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();

  // Redirect if not authenticated
  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  const { data: dashboardData, isLoading } = useQuery(
    'dashboard',
    apiHelpers.getDashboard,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  const { data: usageData } = useQuery(
    'usage',
    apiHelpers.getUsage,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  const { data: projectsData } = useQuery(
    'projects',
    apiHelpers.getProjects,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  if (isLoading) {
    return (
      <DashboardLayout>
        <div className="animate-pulse">
          <div className="h-8 bg-gray-200 rounded w-1/4 mb-6"></div>
          <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4 mb-8">
            {[...Array(4)].map((_, i) => (
              <div key={i} className="bg-white p-6 rounded-lg shadow">
                <div className="h-4 bg-gray-200 rounded w-3/4 mb-2"></div>
                <div className="h-8 bg-gray-200 rounded w-1/2"></div>
              </div>
            ))}
          </div>
        </div>
      </DashboardLayout>
    );
  }

  const stats = [
    {
      name: 'Pending Tasks',
      value: dashboardData?.stats?.pending_tasks || 0,
      icon: ClockIcon,
      color: 'text-yellow-600 bg-yellow-100',
      href: '/tasks?filter=pending',
    },
    {
      name: 'Overdue Tasks',
      value: dashboardData?.stats?.overdue_tasks || 0,
      icon: ExclamationTriangleIcon,
      color: 'text-red-600 bg-red-100',
      href: '/tasks?filter=overdue',
    },
    {
      name: 'Total Meetings',
      value: dashboardData?.stats?.total_meetings || 0,
      icon: MicrophoneIcon,
      color: 'text-blue-600 bg-blue-100',
      href: '/meetings',
    },
    {
      name: 'Completed Tasks',
      value: dashboardData?.pendingActionItems?.filter((item: any) => item.status === 'completed').length || 0,
      icon: CheckCircleIcon,
      color: 'text-green-600 bg-green-100',
      href: '/tasks?filter=completed',
    },
  ];

  return (
    <DashboardLayout>
      <div className="space-y-6">
        {/* Header */}
        <div className="md:flex md:items-center md:justify-between">
          <div className="min-w-0 flex-1">
            <h2 className="text-2xl font-bold leading-7 text-gray-900 sm:truncate sm:text-3xl sm:tracking-tight">
              Dashboard
            </h2>
            <p className="mt-1 text-sm text-gray-500">
              Welcome back! Here's what's happening with your meetings and tasks.
            </p>
          </div>
          <div className="mt-4 flex md:ml-4 md:mt-0">
            <Link
              href="/meetings/upload"
              className="btn btn-primary"
            >
              <PlusIcon className="h-4 w-4 mr-2" />
              Upload Meeting
            </Link>
          </div>
        </div>

        {/* Productivity Widgets */}
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-6 mb-6">
          {/* Timer Widget */}
          <div className="lg:col-span-1">
            <TimerWidget />
          </div>

          {/* Expense Widget */}
          <div className="lg:col-span-1">
            <ExpenseWidget />
          </div>

          {/* Meeting Bot Widget */}
          <div className="lg:col-span-2">
            <MeetingBotWidget />
          </div>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-4">
          {stats.map((stat) => (
            <Link
              key={stat.name}
              href={stat.href}
              className="bg-white overflow-hidden shadow rounded-lg hover:shadow-md transition-shadow"
            >
              <div className="p-5">
                <div className="flex items-center">
                  <div className="flex-shrink-0">
                    <div className={`p-3 rounded-md ${stat.color}`}>
                      <stat.icon className="h-6 w-6" aria-hidden="true" />
                    </div>
                  </div>
                  <div className="ml-5 w-0 flex-1">
                    <dl>
                      <dt className="text-sm font-medium text-gray-500 truncate">
                        {stat.name}
                      </dt>
                      <dd className="text-lg font-medium text-gray-900">
                        {stat.value}
                      </dd>
                    </dl>
                  </div>
                </div>
              </div>
            </Link>
          ))}
        </div>

        {/* Recent Activity */}
        <div className="grid grid-cols-1 gap-6 lg:grid-cols-2">
          {/* Recent Meetings */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Recent Meetings</h3>
            </div>
            <div className="divide-y divide-gray-200">
              {dashboardData?.recentMeetings?.length > 0 ? (
                dashboardData.recentMeetings.slice(0, 5).map((meeting: any) => (
                  <div key={meeting.id} className="px-6 py-4">
                    <div className="flex items-center justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900 truncate">
                          {meeting.title}
                        </p>
                        <p className="text-sm text-gray-500">
                          {formatDate(meeting.created_at)} • {meeting.duration_minutes}m
                        </p>
                      </div>
                      <div className="flex-shrink-0">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          meeting.transcription_status === 'completed' 
                            ? 'bg-green-100 text-green-800'
                            : meeting.transcription_status === 'processing'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-gray-100 text-gray-800'
                        }`}>
                          {meeting.transcription_status}
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="px-6 py-8 text-center">
                  <MicrophoneIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No meetings yet</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    Upload your first meeting recording to get started.
                  </p>
                  <div className="mt-6">
                    <Link href="/meetings/upload" className="btn btn-primary">
                      Upload Meeting
                    </Link>
                  </div>
                </div>
              )}
            </div>
          </div>

          {/* Pending Tasks */}
          <div className="bg-white shadow rounded-lg">
            <div className="px-6 py-4 border-b border-gray-200">
              <h3 className="text-lg font-medium text-gray-900">Pending Tasks</h3>
            </div>
            <div className="divide-y divide-gray-200">
              {dashboardData?.pendingActionItems?.length > 0 ? (
                dashboardData.pendingActionItems.slice(0, 5).map((task: any) => (
                  <div key={task.id} className="px-6 py-4">
                    <div className="flex items-start justify-between">
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-gray-900">
                          {task.task}
                        </p>
                        {task.deadline && (
                          <p className="text-sm text-gray-500">
                            Due: {formatDate(task.deadline)}
                          </p>
                        )}
                      </div>
                      <div className="flex-shrink-0 ml-4">
                        <span className={`inline-flex px-2 py-1 text-xs font-medium rounded-full ${
                          task.priority === 'high' 
                            ? 'bg-red-100 text-red-800'
                            : task.priority === 'medium'
                            ? 'bg-yellow-100 text-yellow-800'
                            : 'bg-green-100 text-green-800'
                        }`}>
                          {task.priority}
                        </span>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="px-6 py-8 text-center">
                  <CheckCircleIcon className="mx-auto h-12 w-12 text-gray-400" />
                  <h3 className="mt-2 text-sm font-medium text-gray-900">No pending tasks</h3>
                  <p className="mt-1 text-sm text-gray-500">
                    All caught up! Record a meeting to generate new action items.
                  </p>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </DashboardLayout>
  );
}
