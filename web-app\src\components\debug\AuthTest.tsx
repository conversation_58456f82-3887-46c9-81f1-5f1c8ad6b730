'use client';

import { useState } from 'react';
import { api } from '@/lib/api';
import { useAuth } from '@/lib/auth';

export default function AuthTest() {
  const [testResults, setTestResults] = useState<any[]>([]);
  const [isLoading, setIsLoading] = useState(false);
  const { signIn, signUp, user, isAuthenticated, token } = useAuth();

  const addResult = (test: string, success: boolean, data?: any, error?: any) => {
    setTestResults(prev => [...prev, {
      test,
      success,
      data,
      error: error?.message || error,
      timestamp: new Date().toISOString()
    }]);
  };

  const testApiConnection = async () => {
    try {
      const response = await api.get('/health');
      addResult('API Health Check', true, response.data);
    } catch (error) {
      addResult('API Health Check', false, null, error);
    }
  };

  const testSignUp = async () => {
    try {
      const testEmail = `test${Date.now()}@example.com`;
      await signUp(testEmail, 'password123', 'Test User');
      addResult('Sign Up', true, { email: testEmail });
    } catch (error) {
      addResult('Sign Up', false, null, error);
    }
  };

  const testSignIn = async () => {
    try {
      await signIn('<EMAIL>', 'password123');
      addResult('Sign In', true, { email: '<EMAIL>' });
    } catch (error) {
      addResult('Sign In', false, null, error);
    }
  };

  const testProtectedEndpoint = async () => {
    try {
      const response = await api.get('/api/users/profile');
      addResult('Protected Endpoint', true, response.data);
    } catch (error) {
      addResult('Protected Endpoint', false, null, error);
    }
  };

  const runAllTests = async () => {
    setIsLoading(true);
    setTestResults([]);
    
    await testApiConnection();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testSignUp();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testSignIn();
    await new Promise(resolve => setTimeout(resolve, 500));
    
    await testProtectedEndpoint();
    
    setIsLoading(false);
  };

  const clearResults = () => {
    setTestResults([]);
  };

  return (
    <div className="p-6 max-w-4xl mx-auto">
      <div className="bg-white rounded-lg shadow-lg p-6">
        <h2 className="text-2xl font-bold text-gray-900 mb-6">Authentication Debug Panel</h2>
        
        {/* Current Auth State */}
        <div className="mb-6 p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-2">Current Auth State</h3>
          <div className="space-y-2 text-sm">
            <div><strong>Authenticated:</strong> {isAuthenticated ? 'Yes' : 'No'}</div>
            <div><strong>User:</strong> {user ? JSON.stringify(user, null, 2) : 'None'}</div>
            <div><strong>Token:</strong> {token ? `${token.substring(0, 20)}...` : 'None'}</div>
          </div>
        </div>

        {/* Test Controls */}
        <div className="mb-6 flex flex-wrap gap-3">
          <button
            onClick={runAllTests}
            disabled={isLoading}
            className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 disabled:opacity-50"
          >
            {isLoading ? 'Running Tests...' : 'Run All Tests'}
          </button>
          
          <button
            onClick={testApiConnection}
            disabled={isLoading}
            className="px-4 py-2 bg-green-600 text-white rounded-lg hover:bg-green-700 disabled:opacity-50"
          >
            Test API Connection
          </button>
          
          <button
            onClick={testSignUp}
            disabled={isLoading}
            className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 disabled:opacity-50"
          >
            Test Sign Up
          </button>
          
          <button
            onClick={testSignIn}
            disabled={isLoading}
            className="px-4 py-2 bg-indigo-600 text-white rounded-lg hover:bg-indigo-700 disabled:opacity-50"
          >
            Test Sign In
          </button>
          
          <button
            onClick={testProtectedEndpoint}
            disabled={isLoading}
            className="px-4 py-2 bg-orange-600 text-white rounded-lg hover:bg-orange-700 disabled:opacity-50"
          >
            Test Protected Endpoint
          </button>
          
          <button
            onClick={clearResults}
            className="px-4 py-2 bg-gray-600 text-white rounded-lg hover:bg-gray-700"
          >
            Clear Results
          </button>
        </div>

        {/* Test Results */}
        <div className="space-y-4">
          <h3 className="text-lg font-semibold">Test Results</h3>
          
          {testResults.length === 0 ? (
            <div className="text-gray-500 italic">No tests run yet. Click "Run All Tests" to start.</div>
          ) : (
            <div className="space-y-3">
              {testResults.map((result, index) => (
                <div
                  key={index}
                  className={`p-4 rounded-lg border-l-4 ${
                    result.success 
                      ? 'bg-green-50 border-green-400' 
                      : 'bg-red-50 border-red-400'
                  }`}
                >
                  <div className="flex items-center justify-between mb-2">
                    <h4 className={`font-semibold ${
                      result.success ? 'text-green-800' : 'text-red-800'
                    }`}>
                      {result.test}
                    </h4>
                    <span className={`px-2 py-1 rounded text-xs font-medium ${
                      result.success 
                        ? 'bg-green-100 text-green-800' 
                        : 'bg-red-100 text-red-800'
                    }`}>
                      {result.success ? 'PASS' : 'FAIL'}
                    </span>
                  </div>
                  
                  {result.data && (
                    <div className="mb-2">
                      <strong className="text-sm">Data:</strong>
                      <pre className="text-xs bg-gray-100 p-2 rounded mt-1 overflow-x-auto">
                        {JSON.stringify(result.data, null, 2)}
                      </pre>
                    </div>
                  )}
                  
                  {result.error && (
                    <div className="mb-2">
                      <strong className="text-sm text-red-700">Error:</strong>
                      <pre className="text-xs bg-red-100 p-2 rounded mt-1 overflow-x-auto text-red-800">
                        {typeof result.error === 'string' ? result.error : JSON.stringify(result.error, null, 2)}
                      </pre>
                    </div>
                  )}
                  
                  <div className="text-xs text-gray-500">
                    {new Date(result.timestamp).toLocaleString()}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
