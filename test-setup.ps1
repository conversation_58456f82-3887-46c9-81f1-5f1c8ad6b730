#!/usr/bin/env pwsh

Write-Host "🧪 Testing KaiNote Setup..." -ForegroundColor Green

# Test 1: Check Node.js version
Write-Host "`n1. Checking Node.js version..." -ForegroundColor Yellow
try {
    $nodeVersion = node --version
    Write-Host "✅ Node.js version: $nodeVersion" -ForegroundColor Green
} catch {
    Write-Host "❌ Node.js not found. Please install Node.js 18+" -ForegroundColor Red
    exit 1
}

# Test 2: Check if dependencies are installed
Write-Host "`n2. Checking dependencies..." -ForegroundColor Yellow
if (Test-Path "node_modules") {
    Write-Host "✅ Root dependencies installed" -ForegroundColor Green
} else {
    Write-Host "❌ Root dependencies missing. Run 'npm install'" -ForegroundColor Red
}

if (Test-Path "web-app/node_modules") {
    Write-Host "✅ Web app dependencies installed" -ForegroundColor Green
} else {
    Write-Host "❌ Web app dependencies missing. Run 'cd web-app && npm install'" -ForegroundColor Red
}

if (Test-Path "api/node_modules") {
    Write-Host "✅ API dependencies installed" -ForegroundColor Green
} else {
    Write-Host "❌ API dependencies missing. Run 'cd api && npm install'" -ForegroundColor Red
}

# Test 3: Check environment files
Write-Host "`n3. Checking environment files..." -ForegroundColor Yellow
if (Test-Path "web-app/.env.local") {
    Write-Host "✅ Web app environment file exists" -ForegroundColor Green
} else {
    Write-Host "❌ Web app .env.local missing" -ForegroundColor Red
}

if (Test-Path "api/.env") {
    Write-Host "✅ API environment file exists" -ForegroundColor Green
} else {
    Write-Host "❌ API .env missing" -ForegroundColor Red
}

# Test 4: Check ports
Write-Host "`n4. Checking if ports are available..." -ForegroundColor Yellow
$port3000 = Get-NetTCPConnection -LocalPort 3000 -ErrorAction SilentlyContinue
$port3003 = Get-NetTCPConnection -LocalPort 3003 -ErrorAction SilentlyContinue

if ($port3000) {
    Write-Host "⚠️ Port 3000 is in use" -ForegroundColor Yellow
} else {
    Write-Host "✅ Port 3000 is available" -ForegroundColor Green
}

if ($port3003) {
    Write-Host "⚠️ Port 3003 is in use" -ForegroundColor Yellow
} else {
    Write-Host "✅ Port 3003 is available" -ForegroundColor Green
}

Write-Host "`n🚀 Setup test complete!" -ForegroundColor Green
Write-Host "To start the application, run: npm run dev" -ForegroundColor Cyan
