import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { data, error } = await supabase
      .from('receipts')
      .select(`
        *,
        invoice:invoices(invoice_number, amount, currency),
        project:projects(name, client_name, client_email)
      `)
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('Error fetching receipt:', error);
      return NextResponse.json(
        { success: false, error: 'Receipt not found' },
        { status: 404 }
      );
    }

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in receipt GET:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    const {
      amount,
      currency,
      payment_method,
      payment_date,
      transaction_id,
      notes
    } = body;

    const updateData: any = {
      updated_at: new Date().toISOString()
    };

    if (amount !== undefined) updateData.amount = parseFloat(amount);
    if (currency !== undefined) updateData.currency = currency;
    if (payment_method !== undefined) updateData.payment_method = payment_method;
    if (payment_date !== undefined) updateData.payment_date = payment_date;
    if (transaction_id !== undefined) updateData.transaction_id = transaction_id;
    if (notes !== undefined) updateData.notes = notes;

    const { data, error } = await supabase
      .from('receipts')
      .update(updateData)
      .eq('id', params.id)
      .select(`
        *,
        invoice:invoices(invoice_number, amount, currency),
        project:projects(name, client_name)
      `)
      .single();

    if (error) {
      console.error('Error updating receipt:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update receipt' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in receipt PUT:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { error } = await supabase
      .from('receipts')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Error deleting receipt:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete receipt' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      message: 'Receipt deleted successfully'
    });

  } catch (error) {
    console.error('Error in receipt DELETE:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
