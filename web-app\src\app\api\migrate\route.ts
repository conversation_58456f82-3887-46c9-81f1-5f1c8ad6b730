import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    console.log('Running database migration for meetings table...');

    // Add missing columns to meetings table
    const migrations = [
      // Add transcript_url column
      `ALTER TABLE public.meetings ADD COLUMN IF NOT EXISTS transcript_url TEXT;`,
      
      // Add transcript column
      `ALTER TABLE public.meetings ADD COLUMN IF NOT EXISTS transcript TEXT;`,
      
      // Add summary column
      `ALTER TABLE public.meetings ADD COLUMN IF NOT EXISTS summary TEXT;`,
      
      // Add client_summary column
      `ALTER TABLE public.meetings ADD COLUMN IF NOT EXISTS client_summary TEXT;`,
      
      // Add shareable_token column
      `ALTER TABLE public.meetings ADD COLUMN IF NOT EXISTS shareable_token TEXT;`,
      
      // Add shareable_expires_at column
      `ALTER TABLE public.meetings ADD COLUMN IF NOT EXISTS shareable_expires_at TIMESTAMP WITH TIME ZONE;`,
      
      // Update platform constraint
      `ALTER TABLE public.meetings DROP CONSTRAINT IF EXISTS meetings_platform_check;`,
      `ALTER TABLE public.meetings ADD CONSTRAINT meetings_platform_check CHECK (platform IN ('google-meet', 'zoom', 'teams', 'webex', 'in-person', 'phone', 'other', 'upload'));`,
      
      // Add description column to action_items if it doesn't exist
      `ALTER TABLE public.action_items ADD COLUMN IF NOT EXISTS description TEXT;`,
      
      // Add assigned_to column to action_items if it doesn't exist
      `ALTER TABLE public.action_items ADD COLUMN IF NOT EXISTS assigned_to TEXT;`,
      
      // Add due_date column to action_items if it doesn't exist
      `ALTER TABLE public.action_items ADD COLUMN IF NOT EXISTS due_date DATE;`,
      
      // Update action_items status constraint
      `ALTER TABLE public.action_items DROP CONSTRAINT IF EXISTS action_items_status_check;`,
      `ALTER TABLE public.action_items ADD CONSTRAINT action_items_status_check CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled'));`
    ];

    const results = [];
    
    for (const migration of migrations) {
      try {
        console.log('Executing:', migration);
        const { data, error } = await supabase.rpc('exec_sql', { sql: migration });
        
        if (error) {
          console.error('Migration error:', error);
          // Some errors are expected (like constraint already exists), so we continue
          results.push({ sql: migration, status: 'error', error: error.message });
        } else {
          console.log('Migration successful:', migration);
          results.push({ sql: migration, status: 'success' });
        }
      } catch (err) {
        console.error('Migration exception:', err);
        results.push({ sql: migration, status: 'exception', error: err instanceof Error ? err.message : 'Unknown error' });
      }
    }

    // Try to create the exec_sql function if it doesn't exist
    try {
      const createFunctionSQL = `
        CREATE OR REPLACE FUNCTION exec_sql(sql text)
        RETURNS void AS $$
        BEGIN
          EXECUTE sql;
        END;
        $$ LANGUAGE plpgsql SECURITY DEFINER;
      `;
      
      await supabase.rpc('exec_sql', { sql: createFunctionSQL });
    } catch (err) {
      console.log('Could not create exec_sql function, trying direct execution...');
    }

    console.log('Migration completed');

    return NextResponse.json({
      success: true,
      message: 'Database migration completed',
      results
    });

  } catch (error) {
    console.error('Error in migration:', error);
    return NextResponse.json(
      { success: false, error: 'Migration failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function GET(request: NextRequest) {
  try {
    // Check current schema
    const { data: columns, error } = await supabase
      .rpc('get_table_columns', { table_name: 'meetings' });

    if (error) {
      // Fallback: try to query the table structure
      const { data: testData, error: testError } = await supabase
        .from('meetings')
        .select('*')
        .limit(1);

      return NextResponse.json({
        success: true,
        message: 'Schema check completed',
        hasColumns: testError ? false : true,
        error: testError?.message
      });
    }

    return NextResponse.json({
      success: true,
      message: 'Schema check completed',
      columns
    });

  } catch (error) {
    console.error('Error checking schema:', error);
    return NextResponse.json(
      { success: false, error: 'Schema check failed', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
