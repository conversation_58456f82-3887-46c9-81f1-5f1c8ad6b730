import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Fetching documents for project:', params.id);

    const { data, error } = await supabase
      .from('project_documents')
      .select('*')
      .eq('project_id', params.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching documents:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch documents', details: error.message },
        { status: 500 }
      );
    }

    console.log('Documents fetched successfully:', data?.length || 0, 'documents');

    return NextResponse.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error in documents GET:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    console.log('Creating document for project:', params.id, body);

    const {
      name,
      file_url,
      file_type,
      file_size,
      content
    } = body;

    // Validate required fields
    if (!name || !file_url) {
      return NextResponse.json(
        { success: false, error: 'Name and file_url are required' },
        { status: 400 }
      );
    }

    // Get user ID from project
    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', params.id)
      .single();

    const documentData = {
      project_id: params.id,
      uploaded_by: project?.user_id || 'user-placeholder',
      name,
      file_url,
      file_type: file_type || 'unknown',
      file_size: file_size || 0,
      content: content || null,
      created_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('project_documents')
      .insert([documentData])
      .select('*')
      .single();

    if (error) {
      console.error('Error creating document:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create document', details: error.message },
        { status: 500 }
      );
    }

    console.log('Document created successfully:', data);

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in documents POST:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
