import { config } from 'dotenv';

// Load test environment variables
config({ path: '.env.test' });

// Set test environment
process.env.NODE_ENV = 'test';

// Mock console methods to reduce noise in tests
global.console = {
  ...console,
  log: jest.fn(),
  debug: jest.fn(),
  info: jest.fn(),
  warn: jest.fn(),
  error: jest.fn(),
};

// Global test timeout
jest.setTimeout(30000);

// Clean up after each test
afterEach(() => {
  jest.clearAllMocks();
});

// Global test utilities
global.testUtils = {
  createMockUser: () => ({
    userId: 'test-user-id',
    email: '<EMAIL>'
  }),
  
  createMockRequest: (overrides = {}) => ({
    user: global.testUtils.createMockUser(),
    body: {},
    params: {},
    query: {},
    headers: {},
    ...overrides
  }),
  
  createMockResponse: () => {
    const res: any = {};
    res.status = jest.fn().mockReturnValue(res);
    res.json = jest.fn().mockReturnValue(res);
    res.send = jest.fn().mockReturnValue(res);
    res.cookie = jest.fn().mockReturnValue(res);
    res.clearCookie = jest.fn().mockReturnValue(res);
    return res;
  }
};

// Extend global types
declare global {
  namespace NodeJS {
    interface Global {
      testUtils: {
        createMockUser: () => { userId: string; email: string };
        createMockRequest: (overrides?: any) => any;
        createMockResponse: () => any;
      };
    }
  }
}
