import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const projectId = searchParams.get('project_id');
    const invoiceId = searchParams.get('invoice_id');

    let query = supabase
      .from('receipts')
      .select(`
        *,
        invoice:invoices(invoice_number, amount, currency),
        project:projects(name, client_name)
      `)
      .order('created_at', { ascending: false });

    if (projectId) {
      query = query.eq('project_id', projectId);
    }

    if (invoiceId) {
      query = query.eq('invoice_id', invoiceId);
    }

    const { data, error } = await query;

    if (error) {
      console.error('Error fetching receipts:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch receipts' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error in receipts GET:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    const {
      project_id,
      invoice_id,
      amount,
      currency = 'USD',
      payment_method,
      payment_date,
      transaction_id,
      notes
    } = body;

    // Validate required fields
    if (!project_id || !invoice_id || !amount || !payment_method || !payment_date) {
      return NextResponse.json(
        { success: false, error: 'Missing required fields' },
        { status: 400 }
      );
    }

    // Get invoice details for receipt number generation
    const { data: invoice } = await supabase
      .from('invoices')
      .select('invoice_number')
      .eq('id', invoice_id)
      .single();

    // Generate receipt number
    const receiptNumber = `RCP-${invoice?.invoice_number?.replace('INV-', '') || Date.now()}`;

    const receiptData = {
      project_id,
      invoice_id,
      receipt_number: receiptNumber,
      amount: parseFloat(amount),
      currency,
      payment_method,
      payment_date,
      transaction_id,
      notes,
      created_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('receipts')
      .insert([receiptData])
      .select(`
        *,
        invoice:invoices(invoice_number, amount, currency),
        project:projects(name, client_name)
      `)
      .single();

    if (error) {
      console.error('Error creating receipt:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create receipt' },
        { status: 500 }
      );
    }

    // Update invoice status to paid if receipt amount matches invoice amount
    if (data) {
      const { data: invoiceData } = await supabase
        .from('invoices')
        .select('amount')
        .eq('id', invoice_id)
        .single();

      if (invoiceData && parseFloat(amount) >= invoiceData.amount) {
        await supabase
          .from('invoices')
          .update({ 
            status: 'paid',
            paid_at: new Date().toISOString()
          })
          .eq('id', invoice_id);
      }
    }

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in receipts POST:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
