import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Fetching invoices for project:', params.id);

    const { data, error } = await supabase
      .from('invoices')
      .select('*')
      .eq('project_id', params.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching invoices:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch invoices', details: error.message },
        { status: 500 }
      );
    }

    console.log('Invoices fetched successfully:', data?.length || 0, 'invoices');

    return NextResponse.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error in invoices GET:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    console.log('Creating invoice for project:', params.id, body);

    const {
      amount,
      currency = 'USD',
      due_date,
      notes,
      status = 'draft'
    } = body;

    // Validate required fields
    if (!amount) {
      return NextResponse.json(
        { success: false, error: 'Amount is required' },
        { status: 400 }
      );
    }

    // Get user ID from project
    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', params.id)
      .single();

    // Generate invoice number
    const invoiceNumber = `INV-${Date.now()}`;

    const invoiceData = {
      project_id: params.id,
      user_id: project?.user_id || 'user-placeholder',
      invoice_number: invoiceNumber,
      amount: parseFloat(amount),
      currency,
      status,
      due_date: due_date || null,
      notes: notes || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('invoices')
      .insert([invoiceData])
      .select('*')
      .single();

    if (error) {
      console.error('Error creating invoice:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create invoice', details: error.message },
        { status: 500 }
      );
    }

    console.log('Invoice created successfully:', data);

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in invoices POST:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
