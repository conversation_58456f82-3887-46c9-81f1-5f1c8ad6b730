import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    console.log('Seeding database with sample data...');

    // Skip user creation for now since it requires auth.users integration
    // In production, users would be created through Supabase Auth

    // Create sample projects
    const projectsData = [
      {
        id: '550e8400-e29b-41d4-a716-************',
        user_id: '550e8400-e29b-41d4-a716-************', // This should match an existing auth.users ID
        name: 'E-commerce Website Redesign',
        description: 'Complete redesign of the company e-commerce platform with modern UI/UX, improved performance, and mobile responsiveness.',
        client_name: 'TechCorp Solutions',
        client_email: '<EMAIL>',
        status: 'active',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-446655440002',
        user_id: '550e8400-e29b-41d4-a716-************',
        name: 'Mobile App Development',
        description: 'Native iOS and Android app for customer engagement and loyalty program management.',
        client_name: 'RetailMax Inc',
        client_email: '<EMAIL>',
        status: 'planning',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    const { data: projects, error: projectError } = await supabase
      .from('projects')
      .upsert(projectsData, { onConflict: 'id' })
      .select();

    if (projectError) {
      console.error('Error creating projects:', projectError);
      return NextResponse.json(
        { success: false, error: 'Failed to create projects', details: projectError.message },
        { status: 500 }
      );
    }

    // Create sample tasks
    const tasksData = [
      {
        id: '550e8400-e29b-41d4-a716-************',
        project_id: '550e8400-e29b-41d4-a716-************',
        user_id: '550e8400-e29b-41d4-a716-************',
        title: 'Design System Creation',
        description: 'Create a comprehensive design system with components, colors, and typography guidelines.',
        status: 'in_progress',
        priority: 'high',
        due_date: new Date(Date.now() + 7 * 24 * 60 * 60 * 1000).toISOString(),
        assigned_to: 'Demo User',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-************',
        project_id: '550e8400-e29b-41d4-a716-************',
        user_id: '550e8400-e29b-41d4-a716-************',
        title: 'Homepage Wireframes',
        description: 'Create detailed wireframes for the new homepage layout and user flow.',
        status: 'completed',
        priority: 'medium',
        completed_at: new Date().toISOString(),
        assigned_to: 'Demo User',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      },
      {
        id: '550e8400-e29b-41d4-a716-************',
        project_id: '550e8400-e29b-41d4-a716-************',
        user_id: '550e8400-e29b-41d4-a716-************',
        title: 'Performance Optimization',
        description: 'Optimize website loading speed and implement caching strategies.',
        status: 'todo',
        priority: 'medium',
        due_date: new Date(Date.now() + 14 * 24 * 60 * 60 * 1000).toISOString(),
        assigned_to: 'Demo User',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    const { error: tasksError } = await supabase
      .from('project_tasks')
      .upsert(tasksData, { onConflict: 'id' });

    if (tasksError) {
      console.error('Error creating tasks:', tasksError);
    }

    // Create sample meetings
    const meetingsData = [
      {
        id: '550e8400-e29b-41d4-a716-446655440020',
        project_id: '550e8400-e29b-41d4-a716-************',
        user_id: '550e8400-e29b-41d4-a716-************',
        title: 'Project Kickoff Meeting',
        platform: 'zoom',
        duration_minutes: 60,
        recorded_at: new Date(Date.now() - 7 * 24 * 60 * 60 * 1000).toISOString(),
        transcription_status: 'completed',
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      }
    ];

    const { error: meetingsError } = await supabase
      .from('meetings')
      .upsert(meetingsData, { onConflict: 'id' });

    if (meetingsError) {
      console.error('Error creating meetings:', meetingsError);
    }

    console.log('Database seeded successfully!');

    return NextResponse.json({
      success: true,
      message: 'Database seeded with sample data',
      data: {
        projects: projects?.length || 0,
        tasks: tasksData.length,
        meetings: meetingsData.length
      }
    });

  } catch (error) {
    console.error('Error seeding database:', error);
    return NextResponse.json(
      { success: false, error: 'Failed to seed database', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
