import { create } from 'zustand';
import { persist } from 'zustand/middleware';
import Cookies from 'js-cookie';
import { api } from './api';

export interface User {
  id: string;
  email: string;
  name: string;
  subscription_tier: 'free' | 'pro';
  hourly_rate?: number;
}

interface AuthState {
  user: User | null;
  token: string | null;
  isLoading: boolean;
  isAuthenticated: boolean;
  
  // Actions
  signIn: (email: string, password: string) => Promise<void>;
  signUp: (email: string, password: string, name: string) => Promise<void>;
  signOut: () => void;
  updateUser: (updates: Partial<User>) => void;
  checkAuth: () => Promise<void>;
}

export const useAuth = create<AuthState>()(
  persist(
    (set, get) => ({
      user: null,
      token: null,
      isLoading: false,
      isAuthenticated: false,

      signIn: async (email: string, password: string) => {
        set({ isLoading: true });

        try {
          const response = await api.post('/api/auth/signin', { email, password });
          const { user, token } = response.data.data;
          
          // Store token in cookie and state
          Cookies.set('auth-token', token, { expires: 7 }); // 7 days
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
          });
          
        } catch (error: any) {
          set({ isLoading: false });
          throw new Error(error.response?.data?.error || 'Sign in failed');
        }
      },

      signUp: async (email: string, password: string, name: string) => {
        set({ isLoading: true });

        try {
          const response = await api.post('/api/auth/signup', { email, password, name });
          const { user, token } = response.data.data;
          
          // Store token in cookie and state
          Cookies.set('auth-token', token, { expires: 7 });
          
          set({
            user,
            token,
            isAuthenticated: true,
            isLoading: false,
          });
          
        } catch (error: any) {
          set({ isLoading: false });
          throw new Error(error.response?.data?.error || 'Sign up failed');
        }
      },

      signOut: () => {
        // Clear token from cookie
        Cookies.remove('auth-token');
        
        set({
          user: null,
          token: null,
          isAuthenticated: false,
        });
      },

      updateUser: (updates: Partial<User>) => {
        const currentUser = get().user;
        if (currentUser) {
          set({
            user: { ...currentUser, ...updates }
          });
        }
      },

      checkAuth: async () => {
        const token = Cookies.get('auth-token');
        
        if (!token) {
          set({ isAuthenticated: false, user: null, token: null });
          return;
        }

        try {
          const response = await api.post('/api/auth/verify', {}, {
            headers: { Authorization: `Bearer ${token}` }
          });
          
          const { user } = response.data.data;
          
          set({
            user,
            token,
            isAuthenticated: true,
          });
          
        } catch (error) {
          // Token is invalid, clear it
          Cookies.remove('auth-token');
          set({
            user: null,
            token: null,
            isAuthenticated: false,
          });
        }
      },
    }),
    {
      name: 'kainote-auth',
      partialize: (state) => ({
        user: state.user,
        token: state.token,
        isAuthenticated: state.isAuthenticated,
      }),
    }
  )
);

// Initialize auth check on app load
if (typeof window !== 'undefined') {
  // Check if we have a token in cookies and sync the state
  const token = Cookies.get('auth-token');
  if (token && !useAuth.getState().isAuthenticated) {
    // If we have a token but aren't authenticated, sync the state
    useAuth.setState({
      token,
      isAuthenticated: true,
      user: {
        id: 'demo-user-id',
        email: '<EMAIL>',
        name: 'Demo User',
        subscription_tier: 'pro'
      }
    });
  }
  useAuth.getState().checkAuth();
}
