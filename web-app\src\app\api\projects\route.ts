import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    console.log('Fetching all projects');

    // For now, we'll get all projects. In production, you'd filter by user
    // TODO: Add proper user authentication and filtering
    const { data, error } = await supabase
      .from('projects')
      .select('*')
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching projects:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch projects', details: error.message },
        { status: 500 }
      );
    }

    console.log('Projects fetched successfully:', data?.length || 0, 'projects');

    return NextResponse.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error in projects GET:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Creating project:', body);

    const {
      name,
      client_name,
      client_email,
      description,
      status = 'active',
      budget,
      deadline
    } = body;

    // Validate required fields
    if (!name || !client_name) {
      return NextResponse.json(
        { success: false, error: 'Name and client name are required' },
        { status: 400 }
      );
    }

    // Get user ID from the first available user in the database
    // In production, you'd extract this from the JWT token
    const { data: users } = await supabase
      .from('users')
      .select('id')
      .limit(1);

    const userId = users && users.length > 0 ? users[0].id : '550e8400-e29b-41d4-a716-446655440000';

    const projectData = {
      user_id: userId,
      name,
      client_name,
      client_email: client_email || null,
      description: description || '',
      status,
      budget: budget ? parseFloat(budget) : null,
      deadline: deadline || null,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('Inserting project data:', projectData);

    const { data, error } = await supabase
      .from('projects')
      .insert([projectData])
      .select('*')
      .single();

    if (error) {
      console.error('Error creating project:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create project', details: error.message },
        { status: 500 }
      );
    }

    console.log('Project created successfully:', data);

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in projects POST:', error);
    return NextResponse.json(
      { 
        success: false, 
        error: 'Internal server error',
        details: error instanceof Error ? error.message : 'Unknown error'
      },
      { status: 500 }
    );
  }
}
