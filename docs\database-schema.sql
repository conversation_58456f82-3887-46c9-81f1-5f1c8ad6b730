-- KaiNote Database Schema for Supabase
-- Run this in your Supabase SQL editor

-- Enable necessary extensions
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";

-- Users table (extends Supabase auth.users)
CREATE TABLE public.users (
  id UUID REFERENCES auth.users(id) PRIMARY KEY,
  email TEXT UNIQUE NOT NULL,
  name TEXT NOT NULL,
  subscription_tier TEXT DEFAULT 'free' CHECK (subscription_tier IN ('free', 'pro')),
  hourly_rate DECIMAL(10,2) DEFAULT 75.00,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Projects table (for organizing meetings by client/project)
CREATE TABLE public.projects (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  client_name TEXT NOT NULL,
  client_email TEXT,
  description TEXT,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'completed', 'on_hold', 'cancelled')),
  budget DECIMAL(10,2),
  deadline TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Meetings table
CREATE TABLE public.meetings (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  platform TEXT DEFAULT 'other' CHECK (platform IN ('google-meet', 'zoom', 'teams', 'other', 'upload')),
  duration_minutes INTEGER DEFAULT 0,
  recorded_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  transcription_status TEXT DEFAULT 'pending' CHECK (transcription_status IN ('pending', 'processing', 'completed', 'failed')),
  audio_url TEXT,
  meeting_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Transcription segments table
CREATE TABLE public.transcription_segments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  meeting_id UUID REFERENCES public.meetings(id) ON DELETE CASCADE,
  speaker TEXT,
  text TEXT NOT NULL,
  start_time DECIMAL(10,3) NOT NULL,
  end_time DECIMAL(10,3) NOT NULL,
  confidence DECIMAL(5,4),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Action items table
CREATE TABLE public.action_items (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  meeting_id UUID REFERENCES public.meetings(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  task TEXT NOT NULL,
  deadline TIMESTAMP WITH TIME ZONE,
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  context TEXT,
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Meeting assessments table
CREATE TABLE public.meeting_assessments (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  meeting_id UUID REFERENCES public.meetings(id) ON DELETE CASCADE,
  is_necessary BOOLEAN DEFAULT true,
  cost_estimate_usd DECIMAL(10,2),
  time_cost_hours DECIMAL(5,2),
  recommendation TEXT,
  async_alternative TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Client summaries table
CREATE TABLE public.client_summaries (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  meeting_id UUID REFERENCES public.meetings(id) ON DELETE CASCADE,
  summary TEXT NOT NULL,
  deliverables JSONB DEFAULT '[]',
  deadlines JSONB DEFAULT '[]',
  next_steps JSONB DEFAULT '[]',
  generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Usage tracking table
CREATE TABLE public.usage_stats (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  month_year TEXT NOT NULL, -- Format: 'YYYY-MM'
  meetings_count INTEGER DEFAULT 0,
  minutes_used INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  UNIQUE(user_id, month_year)
);

-- Reminders table
CREATE TABLE public.reminders (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  action_item_id UUID REFERENCES public.action_items(id) ON DELETE CASCADE,
  reminder_type TEXT CHECK (reminder_type IN ('deadline', 'overdue', 'weekly_digest')),
  scheduled_for TIMESTAMP WITH TIME ZONE NOT NULL,
  sent_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Client access table (for sharing projects with clients)
CREATE TABLE public.client_access (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  client_email TEXT NOT NULL,
  access_token UUID DEFAULT uuid_generate_v4(),
  access_level TEXT DEFAULT 'view' CHECK (access_level IN ('view', 'comment', 'edit')),
  invited_by UUID REFERENCES public.users(id) ON DELETE CASCADE,
  invited_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  last_accessed TIMESTAMP WITH TIME ZONE,
  is_active BOOLEAN DEFAULT true
);

-- Project tasks table (for project management)
CREATE TABLE public.project_tasks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  description TEXT,
  status TEXT DEFAULT 'todo' CHECK (status IN ('todo', 'in_progress', 'review', 'completed')),
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high', 'urgent')),
  assigned_to UUID REFERENCES public.users(id),
  due_date TIMESTAMP WITH TIME ZONE,
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Invoices table
CREATE TABLE public.invoices (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  invoice_number TEXT UNIQUE NOT NULL,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'sent', 'paid', 'overdue', 'cancelled')),
  due_date TIMESTAMP WITH TIME ZONE,
  sent_at TIMESTAMP WITH TIME ZONE,
  paid_at TIMESTAMP WITH TIME ZONE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Project documents table
CREATE TABLE public.project_documents (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  file_url TEXT NOT NULL,
  file_type TEXT,
  file_size INTEGER,
  uploaded_by UUID REFERENCES public.users(id),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Integration settings table
CREATE TABLE public.integrations (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  integration_type TEXT NOT NULL CHECK (integration_type IN ('trello', 'notion', 'calendar', 'slack')),
  settings JSONB DEFAULT '{}',
  access_token TEXT,
  refresh_token TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for better performance
CREATE INDEX idx_projects_user_id ON public.projects(user_id);
CREATE INDEX idx_projects_status ON public.projects(status);
CREATE INDEX idx_meetings_user_id ON public.meetings(user_id);
CREATE INDEX idx_meetings_project_id ON public.meetings(project_id);
CREATE INDEX idx_meetings_created_at ON public.meetings(created_at);
CREATE INDEX idx_transcription_segments_meeting_id ON public.transcription_segments(meeting_id);
CREATE INDEX idx_action_items_user_id ON public.action_items(user_id);
CREATE INDEX idx_action_items_meeting_id ON public.action_items(meeting_id);
CREATE INDEX idx_action_items_status ON public.action_items(status);
CREATE INDEX idx_action_items_deadline ON public.action_items(deadline);
CREATE INDEX idx_usage_stats_user_month ON public.usage_stats(user_id, month_year);
CREATE INDEX idx_reminders_user_scheduled ON public.reminders(user_id, scheduled_for);
CREATE INDEX idx_client_access_project_id ON public.client_access(project_id);
CREATE INDEX idx_client_access_email ON public.client_access(client_email);
CREATE INDEX idx_project_tasks_project_id ON public.project_tasks(project_id);
CREATE INDEX idx_project_tasks_status ON public.project_tasks(status);
CREATE INDEX idx_invoices_project_id ON public.invoices(project_id);
CREATE INDEX idx_invoices_status ON public.invoices(status);
CREATE INDEX idx_project_documents_project_id ON public.project_documents(project_id);
CREATE INDEX idx_integrations_user_id ON public.integrations(user_id);

-- Time tracking tables
CREATE TABLE public.time_entries (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  task_id UUID REFERENCES public.project_tasks(id) ON DELETE SET NULL,
  meeting_id UUID REFERENCES public.meetings(id) ON DELETE SET NULL,
  description TEXT,
  start_time TIMESTAMP WITH TIME ZONE NOT NULL,
  end_time TIMESTAMP WITH TIME ZONE,
  duration_minutes INTEGER, -- calculated field for completed entries
  hourly_rate DECIMAL(10,2), -- rate at time of entry
  is_billable BOOLEAN DEFAULT true,
  is_running BOOLEAN DEFAULT false, -- true for active timers
  tags TEXT[], -- for categorization
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Time tracking templates for common tasks
CREATE TABLE public.time_templates (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  default_project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
  default_hourly_rate DECIMAL(10,2),
  default_billable BOOLEAN DEFAULT true,
  default_tags TEXT[],
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Time tracking goals and targets
CREATE TABLE public.time_goals (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  goal_type TEXT DEFAULT 'weekly' CHECK (goal_type IN ('daily', 'weekly', 'monthly', 'project')),
  target_hours DECIMAL(5,2) NOT NULL,
  target_revenue DECIMAL(10,2),
  period_start DATE,
  period_end DATE,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for time tracking tables
CREATE INDEX idx_time_entries_user_id ON public.time_entries(user_id);
CREATE INDEX idx_time_entries_project_id ON public.time_entries(project_id);
CREATE INDEX idx_time_entries_start_time ON public.time_entries(start_time);
CREATE INDEX idx_time_entries_is_running ON public.time_entries(is_running);
CREATE INDEX idx_time_entries_is_billable ON public.time_entries(is_billable);
CREATE INDEX idx_time_templates_user_id ON public.time_templates(user_id);
CREATE INDEX idx_time_goals_user_id ON public.time_goals(user_id);
CREATE INDEX idx_time_goals_project_id ON public.time_goals(project_id);

-- Expense tracking tables
CREATE TABLE public.expense_categories (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  color TEXT DEFAULT '#6B7280', -- hex color for UI
  is_tax_deductible BOOLEAN DEFAULT true,
  parent_category_id UUID REFERENCES public.expense_categories(id) ON DELETE SET NULL,
  is_default BOOLEAN DEFAULT false, -- system default categories
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

CREATE TABLE public.expenses (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
  category_id UUID REFERENCES public.expense_categories(id) ON DELETE SET NULL,
  title TEXT NOT NULL,
  description TEXT,
  amount DECIMAL(10,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  expense_date DATE NOT NULL,
  vendor TEXT, -- who was paid
  payment_method TEXT, -- cash, credit_card, bank_transfer, etc.
  receipt_url TEXT, -- URL to uploaded receipt image
  receipt_filename TEXT,
  is_billable BOOLEAN DEFAULT false, -- can be billed to client
  is_tax_deductible BOOLEAN DEFAULT true,
  tax_category TEXT, -- business_expense, office_supplies, travel, etc.
  notes TEXT,
  tags TEXT[],
  status TEXT DEFAULT 'pending' CHECK (status IN ('pending', 'approved', 'rejected', 'reimbursed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Expense budgets and limits
CREATE TABLE public.expense_budgets (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  category_id UUID REFERENCES public.expense_categories(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE CASCADE,
  budget_type TEXT DEFAULT 'monthly' CHECK (budget_type IN ('weekly', 'monthly', 'quarterly', 'yearly', 'project')),
  budget_amount DECIMAL(10,2) NOT NULL,
  spent_amount DECIMAL(10,2) DEFAULT 0,
  period_start DATE,
  period_end DATE,
  alert_threshold DECIMAL(5,2) DEFAULT 80.00, -- alert when 80% of budget is used
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Expense reports for tax/accounting
CREATE TABLE public.expense_reports (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  title TEXT NOT NULL,
  report_type TEXT DEFAULT 'tax' CHECK (report_type IN ('tax', 'client', 'project', 'custom')),
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  total_amount DECIMAL(10,2) DEFAULT 0,
  tax_deductible_amount DECIMAL(10,2) DEFAULT 0,
  billable_amount DECIMAL(10,2) DEFAULT 0,
  expense_ids UUID[], -- array of expense IDs included in report
  notes TEXT,
  status TEXT DEFAULT 'draft' CHECK (status IN ('draft', 'finalized', 'submitted')),
  generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for expense tracking tables
CREATE INDEX idx_expense_categories_user_id ON public.expense_categories(user_id);
CREATE INDEX idx_expense_categories_parent_id ON public.expense_categories(parent_category_id);
CREATE INDEX idx_expenses_user_id ON public.expenses(user_id);
CREATE INDEX idx_expenses_project_id ON public.expenses(project_id);
CREATE INDEX idx_expenses_category_id ON public.expenses(category_id);
CREATE INDEX idx_expenses_date ON public.expenses(expense_date);
CREATE INDEX idx_expenses_amount ON public.expenses(amount);
CREATE INDEX idx_expenses_billable ON public.expenses(is_billable);
CREATE INDEX idx_expenses_tax_deductible ON public.expenses(is_tax_deductible);
CREATE INDEX idx_expense_budgets_user_id ON public.expense_budgets(user_id);
CREATE INDEX idx_expense_budgets_category_id ON public.expense_budgets(category_id);
CREATE INDEX idx_expense_reports_user_id ON public.expense_reports(user_id);

-- Client management tables
CREATE TABLE public.clients (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  company TEXT,
  email TEXT,
  phone TEXT,
  address TEXT,
  website TEXT,
  industry TEXT,
  client_type TEXT DEFAULT 'individual' CHECK (client_type IN ('individual', 'company', 'agency')),
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'inactive', 'prospect', 'archived')),
  hourly_rate DECIMAL(10,2),
  currency TEXT DEFAULT 'USD',
  payment_terms TEXT DEFAULT 'net_30',
  tax_id TEXT,
  notes TEXT,
  tags TEXT[],
  avatar_url TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Client contacts (multiple contacts per client)
CREATE TABLE public.client_contacts (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  email TEXT,
  phone TEXT,
  role TEXT, -- primary, billing, technical, etc.
  is_primary BOOLEAN DEFAULT false,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Communication history
CREATE TABLE public.client_communications (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
  meeting_id UUID REFERENCES public.meetings(id) ON DELETE SET NULL,
  type TEXT DEFAULT 'email' CHECK (type IN ('email', 'call', 'meeting', 'message', 'note')),
  subject TEXT,
  content TEXT,
  direction TEXT DEFAULT 'outbound' CHECK (direction IN ('inbound', 'outbound')),
  status TEXT DEFAULT 'sent' CHECK (status IN ('draft', 'sent', 'delivered', 'read', 'replied')),
  scheduled_at TIMESTAMP WITH TIME ZONE,
  sent_at TIMESTAMP WITH TIME ZONE,
  metadata JSONB DEFAULT '{}', -- email headers, call duration, etc.
  attachments TEXT[], -- file URLs
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Client portal access
CREATE TABLE public.client_portal_access (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
  access_token TEXT UNIQUE NOT NULL,
  email TEXT NOT NULL,
  is_active BOOLEAN DEFAULT true,
  last_accessed TIMESTAMP WITH TIME ZONE,
  expires_at TIMESTAMP WITH TIME ZONE,
  permissions JSONB DEFAULT '{"view_projects": true, "view_invoices": true, "view_meetings": false}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Email templates for client communication
CREATE TABLE public.email_templates (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  subject TEXT NOT NULL,
  content TEXT NOT NULL,
  template_type TEXT DEFAULT 'general' CHECK (template_type IN ('general', 'project_update', 'invoice', 'follow_up', 'onboarding')),
  variables TEXT[], -- available template variables
  is_default BOOLEAN DEFAULT false,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Client onboarding workflows
CREATE TABLE public.client_onboarding (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  client_id UUID REFERENCES public.clients(id) ON DELETE CASCADE,
  workflow_name TEXT NOT NULL,
  current_step INTEGER DEFAULT 1,
  total_steps INTEGER NOT NULL,
  status TEXT DEFAULT 'in_progress' CHECK (status IN ('not_started', 'in_progress', 'completed', 'paused')),
  steps JSONB NOT NULL, -- workflow steps configuration
  completed_steps INTEGER[] DEFAULT '{}',
  due_date DATE,
  notes TEXT,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for client management tables
CREATE INDEX idx_clients_user_id ON public.clients(user_id);
CREATE INDEX idx_clients_status ON public.clients(status);
CREATE INDEX idx_clients_email ON public.clients(email);
CREATE INDEX idx_client_contacts_client_id ON public.client_contacts(client_id);
CREATE INDEX idx_client_contacts_email ON public.client_contacts(email);
CREATE INDEX idx_client_communications_user_id ON public.client_communications(user_id);
CREATE INDEX idx_client_communications_client_id ON public.client_communications(client_id);
CREATE INDEX idx_client_communications_type ON public.client_communications(type);
CREATE INDEX idx_client_communications_created_at ON public.client_communications(created_at);
CREATE INDEX idx_client_portal_access_client_id ON public.client_portal_access(client_id);
CREATE INDEX idx_client_portal_access_token ON public.client_portal_access(access_token);
CREATE INDEX idx_email_templates_user_id ON public.email_templates(user_id);
CREATE INDEX idx_client_onboarding_user_id ON public.client_onboarding(user_id);
CREATE INDEX idx_client_onboarding_client_id ON public.client_onboarding(client_id);

-- Financial analytics tables
CREATE TABLE public.financial_goals (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  goal_type TEXT DEFAULT 'revenue' CHECK (goal_type IN ('revenue', 'profit', 'expenses', 'hours')),
  period_type TEXT DEFAULT 'monthly' CHECK (period_type IN ('weekly', 'monthly', 'quarterly', 'yearly')),
  target_amount DECIMAL(12,2) NOT NULL,
  current_amount DECIMAL(12,2) DEFAULT 0,
  currency TEXT DEFAULT 'USD',
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  description TEXT,
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Financial transactions (unified view of income/expenses)
CREATE TABLE public.financial_transactions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
  client_id UUID REFERENCES public.clients(id) ON DELETE SET NULL,
  invoice_id UUID REFERENCES public.invoices(id) ON DELETE SET NULL,
  expense_id UUID REFERENCES public.expenses(id) ON DELETE SET NULL,
  time_entry_id UUID REFERENCES public.time_entries(id) ON DELETE SET NULL,
  transaction_type TEXT NOT NULL CHECK (transaction_type IN ('income', 'expense', 'refund', 'adjustment')),
  category TEXT, -- revenue, expense_category, etc.
  amount DECIMAL(12,2) NOT NULL,
  currency TEXT DEFAULT 'USD',
  description TEXT,
  transaction_date DATE NOT NULL,
  is_billable BOOLEAN DEFAULT false,
  is_tax_deductible BOOLEAN DEFAULT false,
  status TEXT DEFAULT 'confirmed' CHECK (status IN ('pending', 'confirmed', 'cancelled')),
  metadata JSONB DEFAULT '{}',
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Financial reports cache
CREATE TABLE public.financial_reports (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  report_type TEXT NOT NULL CHECK (report_type IN ('profit_loss', 'cash_flow', 'revenue_analysis', 'expense_analysis')),
  period_start DATE NOT NULL,
  period_end DATE NOT NULL,
  data JSONB NOT NULL,
  generated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  expires_at TIMESTAMP WITH TIME ZONE DEFAULT (NOW() + INTERVAL '1 hour')
);

-- Budget tracking
CREATE TABLE public.budgets (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  budget_type TEXT DEFAULT 'expense' CHECK (budget_type IN ('expense', 'revenue', 'project')),
  category TEXT, -- expense category, revenue stream, etc.
  period_type TEXT DEFAULT 'monthly' CHECK (period_type IN ('weekly', 'monthly', 'quarterly', 'yearly')),
  budget_amount DECIMAL(12,2) NOT NULL,
  spent_amount DECIMAL(12,2) DEFAULT 0,
  currency TEXT DEFAULT 'USD',
  start_date DATE NOT NULL,
  end_date DATE NOT NULL,
  alert_threshold DECIMAL(5,2) DEFAULT 80.00, -- alert when 80% of budget is used
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for financial analytics tables
CREATE INDEX idx_financial_goals_user_id ON public.financial_goals(user_id);
CREATE INDEX idx_financial_goals_period ON public.financial_goals(start_date, end_date);
CREATE INDEX idx_financial_transactions_user_id ON public.financial_transactions(user_id);
CREATE INDEX idx_financial_transactions_type ON public.financial_transactions(transaction_type);
CREATE INDEX idx_financial_transactions_date ON public.financial_transactions(transaction_date);
CREATE INDEX idx_financial_transactions_project_id ON public.financial_transactions(project_id);
CREATE INDEX idx_financial_transactions_client_id ON public.financial_transactions(client_id);
CREATE INDEX idx_financial_reports_user_id ON public.financial_reports(user_id);
CREATE INDEX idx_financial_reports_type_period ON public.financial_reports(report_type, period_start, period_end);
CREATE INDEX idx_budgets_user_id ON public.budgets(user_id);
CREATE INDEX idx_budgets_period ON public.budgets(start_date, end_date);

-- Workflow automation tables
CREATE TABLE public.automation_rules (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  trigger_type TEXT NOT NULL CHECK (trigger_type IN ('time_based', 'event_based', 'condition_based')),
  trigger_config JSONB NOT NULL, -- trigger configuration
  action_type TEXT NOT NULL CHECK (action_type IN ('send_email', 'create_task', 'update_status', 'send_invoice', 'create_reminder')),
  action_config JSONB NOT NULL, -- action configuration
  conditions JSONB DEFAULT '[]', -- array of conditions
  is_active BOOLEAN DEFAULT true,
  last_executed TIMESTAMP WITH TIME ZONE,
  execution_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Automation executions log
CREATE TABLE public.automation_executions (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  rule_id UUID REFERENCES public.automation_rules(id) ON DELETE CASCADE,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  trigger_data JSONB, -- data that triggered the automation
  execution_status TEXT DEFAULT 'pending' CHECK (execution_status IN ('pending', 'running', 'completed', 'failed')),
  result_data JSONB, -- result of the automation
  error_message TEXT,
  executed_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Recurring tasks
CREATE TABLE public.recurring_tasks (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
  client_id UUID REFERENCES public.clients(id) ON DELETE SET NULL,
  template_name TEXT NOT NULL,
  task_title TEXT NOT NULL,
  task_description TEXT,
  recurrence_pattern TEXT NOT NULL CHECK (recurrence_pattern IN ('daily', 'weekly', 'monthly', 'quarterly', 'yearly')),
  recurrence_config JSONB DEFAULT '{}', -- specific recurrence settings
  next_due_date DATE NOT NULL,
  last_created_date DATE,
  priority TEXT DEFAULT 'medium' CHECK (priority IN ('low', 'medium', 'high')),
  estimated_hours DECIMAL(5,2),
  is_active BOOLEAN DEFAULT true,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflow templates
CREATE TABLE public.workflow_templates (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  name TEXT NOT NULL,
  description TEXT,
  category TEXT DEFAULT 'general' CHECK (category IN ('general', 'project', 'client', 'invoice', 'meeting')),
  steps JSONB NOT NULL, -- array of workflow steps
  is_default BOOLEAN DEFAULT false,
  is_active BOOLEAN DEFAULT true,
  usage_count INTEGER DEFAULT 0,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  updated_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Workflow instances (active workflows)
CREATE TABLE public.workflow_instances (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  template_id UUID REFERENCES public.workflow_templates(id) ON DELETE SET NULL,
  project_id UUID REFERENCES public.projects(id) ON DELETE SET NULL,
  client_id UUID REFERENCES public.clients(id) ON DELETE SET NULL,
  name TEXT NOT NULL,
  current_step INTEGER DEFAULT 1,
  total_steps INTEGER NOT NULL,
  status TEXT DEFAULT 'active' CHECK (status IN ('active', 'paused', 'completed', 'cancelled')),
  step_data JSONB DEFAULT '{}', -- data for each step
  completed_steps INTEGER[] DEFAULT '{}',
  due_date DATE,
  started_at TIMESTAMP WITH TIME ZONE DEFAULT NOW(),
  completed_at TIMESTAMP WITH TIME ZONE,
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Automated follow-ups
CREATE TABLE public.automated_followups (
  id UUID DEFAULT uuid_generate_v4() PRIMARY KEY,
  user_id UUID REFERENCES public.users(id) ON DELETE CASCADE,
  target_type TEXT NOT NULL CHECK (target_type IN ('invoice', 'project', 'client', 'task')),
  target_id UUID NOT NULL, -- ID of the target object
  followup_type TEXT NOT NULL CHECK (followup_type IN ('overdue_invoice', 'project_update', 'task_reminder', 'client_checkin')),
  trigger_condition JSONB NOT NULL, -- when to trigger the follow-up
  email_template_id UUID REFERENCES public.email_templates(id) ON DELETE SET NULL,
  custom_message TEXT,
  scheduled_date DATE,
  sent_date DATE,
  status TEXT DEFAULT 'scheduled' CHECK (status IN ('scheduled', 'sent', 'cancelled', 'failed')),
  created_at TIMESTAMP WITH TIME ZONE DEFAULT NOW()
);

-- Indexes for automation tables
CREATE INDEX idx_automation_rules_user_id ON public.automation_rules(user_id);
CREATE INDEX idx_automation_rules_trigger_type ON public.automation_rules(trigger_type);
CREATE INDEX idx_automation_rules_active ON public.automation_rules(is_active);
CREATE INDEX idx_automation_executions_rule_id ON public.automation_executions(rule_id);
CREATE INDEX idx_automation_executions_user_id ON public.automation_executions(user_id);
CREATE INDEX idx_automation_executions_status ON public.automation_executions(execution_status);
CREATE INDEX idx_recurring_tasks_user_id ON public.recurring_tasks(user_id);
CREATE INDEX idx_recurring_tasks_next_due ON public.recurring_tasks(next_due_date);
CREATE INDEX idx_recurring_tasks_active ON public.recurring_tasks(is_active);
CREATE INDEX idx_workflow_templates_user_id ON public.workflow_templates(user_id);
CREATE INDEX idx_workflow_templates_category ON public.workflow_templates(category);
CREATE INDEX idx_workflow_instances_user_id ON public.workflow_instances(user_id);
CREATE INDEX idx_workflow_instances_status ON public.workflow_instances(status);
CREATE INDEX idx_automated_followups_user_id ON public.automated_followups(user_id);
CREATE INDEX idx_automated_followups_target ON public.automated_followups(target_type, target_id);
CREATE INDEX idx_automated_followups_scheduled ON public.automated_followups(scheduled_date);

-- Row Level Security (RLS) policies
ALTER TABLE public.users ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.projects ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.meetings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.transcription_segments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.action_items ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.meeting_assessments ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_summaries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.usage_stats ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.reminders ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.invoices ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.project_documents ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.integrations ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_entries ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.time_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expense_categories ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expenses ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expense_budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.expense_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.clients ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_contacts ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_communications ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_portal_access ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.email_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.client_onboarding ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.financial_goals ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.financial_transactions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.financial_reports ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.budgets ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.automation_rules ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.automation_executions ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.recurring_tasks ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workflow_templates ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.workflow_instances ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.automated_followups ENABLE ROW LEVEL SECURITY;

-- Users can only access their own data
CREATE POLICY "Users can view own profile" ON public.users FOR SELECT USING (auth.uid() = id);
CREATE POLICY "Users can update own profile" ON public.users FOR UPDATE USING (auth.uid() = id);

CREATE POLICY "Users can view own projects" ON public.projects FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own projects" ON public.projects FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own projects" ON public.projects FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own projects" ON public.projects FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own meetings" ON public.meetings FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own meetings" ON public.meetings FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own meetings" ON public.meetings FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own meetings" ON public.meetings FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own transcriptions" ON public.transcription_segments FOR SELECT USING (
  auth.uid() IN (SELECT user_id FROM public.meetings WHERE id = meeting_id)
);
CREATE POLICY "Users can insert own transcriptions" ON public.transcription_segments FOR INSERT WITH CHECK (
  auth.uid() IN (SELECT user_id FROM public.meetings WHERE id = meeting_id)
);

CREATE POLICY "Users can view own action items" ON public.action_items FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own action items" ON public.action_items FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own action items" ON public.action_items FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own action items" ON public.action_items FOR DELETE USING (auth.uid() = user_id);

-- Similar policies for other tables...
CREATE POLICY "Users can view own assessments" ON public.meeting_assessments FOR SELECT USING (
  auth.uid() IN (SELECT user_id FROM public.meetings WHERE id = meeting_id)
);

CREATE POLICY "Users can view own summaries" ON public.client_summaries FOR SELECT USING (
  auth.uid() IN (SELECT user_id FROM public.meetings WHERE id = meeting_id)
);

CREATE POLICY "Users can view own usage" ON public.usage_stats FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can view own reminders" ON public.reminders FOR SELECT USING (auth.uid() = user_id);

-- Time tracking policies
CREATE POLICY "Users can view own time entries" ON public.time_entries FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own time entries" ON public.time_entries FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own time entries" ON public.time_entries FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own time entries" ON public.time_entries FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own time templates" ON public.time_templates FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own time templates" ON public.time_templates FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own time templates" ON public.time_templates FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own time templates" ON public.time_templates FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own time goals" ON public.time_goals FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own time goals" ON public.time_goals FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own time goals" ON public.time_goals FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own time goals" ON public.time_goals FOR DELETE USING (auth.uid() = user_id);

-- Expense tracking policies
CREATE POLICY "Users can view own expense categories" ON public.expense_categories FOR SELECT USING (auth.uid() = user_id OR is_default = true);
CREATE POLICY "Users can insert own expense categories" ON public.expense_categories FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own expense categories" ON public.expense_categories FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own expense categories" ON public.expense_categories FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own expenses" ON public.expenses FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own expenses" ON public.expenses FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own expenses" ON public.expenses FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own expenses" ON public.expenses FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own expense budgets" ON public.expense_budgets FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own expense budgets" ON public.expense_budgets FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own expense budgets" ON public.expense_budgets FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own expense budgets" ON public.expense_budgets FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own expense reports" ON public.expense_reports FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own expense reports" ON public.expense_reports FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own expense reports" ON public.expense_reports FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own expense reports" ON public.expense_reports FOR DELETE USING (auth.uid() = user_id);

-- Client management policies
CREATE POLICY "Users can view own clients" ON public.clients FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own clients" ON public.clients FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own clients" ON public.clients FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own clients" ON public.clients FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view client contacts" ON public.client_contacts FOR SELECT USING (
  EXISTS (SELECT 1 FROM public.clients WHERE clients.id = client_contacts.client_id AND clients.user_id = auth.uid())
);
CREATE POLICY "Users can insert client contacts" ON public.client_contacts FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM public.clients WHERE clients.id = client_contacts.client_id AND clients.user_id = auth.uid())
);
CREATE POLICY "Users can update client contacts" ON public.client_contacts FOR UPDATE USING (
  EXISTS (SELECT 1 FROM public.clients WHERE clients.id = client_contacts.client_id AND clients.user_id = auth.uid())
);
CREATE POLICY "Users can delete client contacts" ON public.client_contacts FOR DELETE USING (
  EXISTS (SELECT 1 FROM public.clients WHERE clients.id = client_contacts.client_id AND clients.user_id = auth.uid())
);

CREATE POLICY "Users can view own client communications" ON public.client_communications FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own client communications" ON public.client_communications FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own client communications" ON public.client_communications FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own client communications" ON public.client_communications FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view client portal access" ON public.client_portal_access FOR SELECT USING (
  EXISTS (SELECT 1 FROM public.clients WHERE clients.id = client_portal_access.client_id AND clients.user_id = auth.uid())
);
CREATE POLICY "Users can insert client portal access" ON public.client_portal_access FOR INSERT WITH CHECK (
  EXISTS (SELECT 1 FROM public.clients WHERE clients.id = client_portal_access.client_id AND clients.user_id = auth.uid())
);
CREATE POLICY "Users can update client portal access" ON public.client_portal_access FOR UPDATE USING (
  EXISTS (SELECT 1 FROM public.clients WHERE clients.id = client_portal_access.client_id AND clients.user_id = auth.uid())
);
CREATE POLICY "Users can delete client portal access" ON public.client_portal_access FOR DELETE USING (
  EXISTS (SELECT 1 FROM public.clients WHERE clients.id = client_portal_access.client_id AND clients.user_id = auth.uid())
);

CREATE POLICY "Users can view own email templates" ON public.email_templates FOR SELECT USING (auth.uid() = user_id OR is_default = true);
CREATE POLICY "Users can insert own email templates" ON public.email_templates FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own email templates" ON public.email_templates FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own email templates" ON public.email_templates FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own client onboarding" ON public.client_onboarding FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own client onboarding" ON public.client_onboarding FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own client onboarding" ON public.client_onboarding FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own client onboarding" ON public.client_onboarding FOR DELETE USING (auth.uid() = user_id);

-- Financial analytics policies
CREATE POLICY "Users can view own financial goals" ON public.financial_goals FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own financial goals" ON public.financial_goals FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own financial goals" ON public.financial_goals FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own financial goals" ON public.financial_goals FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own financial transactions" ON public.financial_transactions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own financial transactions" ON public.financial_transactions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own financial transactions" ON public.financial_transactions FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own financial transactions" ON public.financial_transactions FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own financial reports" ON public.financial_reports FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own financial reports" ON public.financial_reports FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own financial reports" ON public.financial_reports FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own financial reports" ON public.financial_reports FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own budgets" ON public.budgets FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own budgets" ON public.budgets FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own budgets" ON public.budgets FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own budgets" ON public.budgets FOR DELETE USING (auth.uid() = user_id);

-- Workflow automation policies
CREATE POLICY "Users can view own automation rules" ON public.automation_rules FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own automation rules" ON public.automation_rules FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own automation rules" ON public.automation_rules FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own automation rules" ON public.automation_rules FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own automation executions" ON public.automation_executions FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own automation executions" ON public.automation_executions FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own automation executions" ON public.automation_executions FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own automation executions" ON public.automation_executions FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own recurring tasks" ON public.recurring_tasks FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own recurring tasks" ON public.recurring_tasks FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own recurring tasks" ON public.recurring_tasks FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own recurring tasks" ON public.recurring_tasks FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view workflow templates" ON public.workflow_templates FOR SELECT USING (auth.uid() = user_id OR is_default = true);
CREATE POLICY "Users can insert own workflow templates" ON public.workflow_templates FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own workflow templates" ON public.workflow_templates FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own workflow templates" ON public.workflow_templates FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own workflow instances" ON public.workflow_instances FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own workflow instances" ON public.workflow_instances FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own workflow instances" ON public.workflow_instances FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own workflow instances" ON public.workflow_instances FOR DELETE USING (auth.uid() = user_id);

CREATE POLICY "Users can view own automated followups" ON public.automated_followups FOR SELECT USING (auth.uid() = user_id);
CREATE POLICY "Users can insert own automated followups" ON public.automated_followups FOR INSERT WITH CHECK (auth.uid() = user_id);
CREATE POLICY "Users can update own automated followups" ON public.automated_followups FOR UPDATE USING (auth.uid() = user_id);
CREATE POLICY "Users can delete own automated followups" ON public.automated_followups FOR DELETE USING (auth.uid() = user_id);

-- Functions for automatic timestamp updates
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
  NEW.updated_at = NOW();
  RETURN NEW;
END;
$$ language 'plpgsql';

-- Triggers for updated_at
CREATE TRIGGER update_users_updated_at BEFORE UPDATE ON public.users FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_projects_updated_at BEFORE UPDATE ON public.projects FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_meetings_updated_at BEFORE UPDATE ON public.meetings FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_action_items_updated_at BEFORE UPDATE ON public.action_items FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_usage_stats_updated_at BEFORE UPDATE ON public.usage_stats FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_time_entries_updated_at BEFORE UPDATE ON public.time_entries FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_expenses_updated_at BEFORE UPDATE ON public.expenses FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_clients_updated_at BEFORE UPDATE ON public.clients FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_client_communications_updated_at BEFORE UPDATE ON public.client_communications FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_email_templates_updated_at BEFORE UPDATE ON public.email_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_client_onboarding_updated_at BEFORE UPDATE ON public.client_onboarding FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_financial_goals_updated_at BEFORE UPDATE ON public.financial_goals FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_budgets_updated_at BEFORE UPDATE ON public.budgets FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_automation_rules_updated_at BEFORE UPDATE ON public.automation_rules FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_recurring_tasks_updated_at BEFORE UPDATE ON public.recurring_tasks FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
CREATE TRIGGER update_workflow_templates_updated_at BEFORE UPDATE ON public.workflow_templates FOR EACH ROW EXECUTE FUNCTION update_updated_at_column();
