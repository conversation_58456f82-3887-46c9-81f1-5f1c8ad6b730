// Database model validation tests
describe('Database Models', () => {
  describe('User Model', () => {
    interface User {
      id?: string;
      email: string;
      name: string;
      password?: string;
      created_at?: string;
      updated_at?: string;
    }

    const validateUser = (user: Partial<User>): { isValid: boolean; errors: string[] } => {
      const errors: string[] = [];

      if (!user.email) {
        errors.push('Email is required');
      } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(user.email)) {
        errors.push('Invalid email format');
      }

      if (!user.name) {
        errors.push('Name is required');
      } else if (user.name.length < 2) {
        errors.push('Name must be at least 2 characters');
      }

      return { isValid: errors.length === 0, errors };
    };

    it('should validate correct user data', () => {
      const validUsers = [
        { email: '<EMAIL>', name: 'Test User' },
        { email: '<EMAIL>', name: '<PERSON>' },
        { email: '<EMAIL>', name: 'Administrator' }
      ];

      validUsers.forEach(user => {
        const result = validateUser(user);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('should reject invalid user data', () => {
      const invalidUsers = [
        { data: {}, expectedErrors: ['Email is required', 'Name is required'] },
        { data: { email: 'invalid-email' }, expectedErrors: ['Invalid email format', 'Name is required'] },
        { data: { email: '<EMAIL>', name: 'A' }, expectedErrors: ['Name must be at least 2 characters'] }
      ];

      invalidUsers.forEach(({ data, expectedErrors }) => {
        const result = validateUser(data);
        expect(result.isValid).toBe(false);
        expectedErrors.forEach(error => {
          expect(result.errors).toContain(error);
        });
      });
    });
  });

  describe('Project Model', () => {
    interface Project {
      id?: string;
      name: string;
      description?: string;
      status: 'active' | 'completed' | 'on_hold' | 'cancelled';
      client_name?: string;
      hourly_rate?: number;
      user_id: string;
      created_at?: string;
      updated_at?: string;
    }

    const validateProject = (project: Partial<Project>): { isValid: boolean; errors: string[] } => {
      const errors: string[] = [];

      if (!project.name) {
        errors.push('Project name is required');
      } else if (project.name.length > 100) {
        errors.push('Project name must be less than 100 characters');
      }

      if (!project.user_id) {
        errors.push('User ID is required');
      }

      if (project.status && !['active', 'completed', 'on_hold', 'cancelled'].includes(project.status)) {
        errors.push('Invalid project status');
      }

      if (project.hourly_rate !== undefined && (project.hourly_rate < 0 || isNaN(project.hourly_rate))) {
        errors.push('Hourly rate must be a positive number');
      }

      return { isValid: errors.length === 0, errors };
    };

    it('should validate correct project data', () => {
      const validProjects = [
        {
          name: 'Test Project',
          status: 'active' as const,
          user_id: 'user-123',
          hourly_rate: 75.00
        },
        {
          name: 'Another Project',
          status: 'completed' as const,
          user_id: 'user-456',
          description: 'Project description'
        }
      ];

      validProjects.forEach(project => {
        const result = validateProject(project);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('should reject invalid project data', () => {
      const invalidProjects = [
        { 
          data: { name: 'Test' }, 
          expectedErrors: ['User ID is required'] 
        },
        { 
          data: { name: '', user_id: 'user-123' }, 
          expectedErrors: ['Project name is required'] 
        },
        {
          data: { name: 'Test', user_id: 'user-123', status: 'invalid' as any },
          expectedErrors: ['Invalid project status']
        },
        { 
          data: { name: 'Test', user_id: 'user-123', hourly_rate: -50 }, 
          expectedErrors: ['Hourly rate must be a positive number'] 
        }
      ];

      invalidProjects.forEach(({ data, expectedErrors }) => {
        const result = validateProject(data);
        expect(result.isValid).toBe(false);
        expectedErrors.forEach(error => {
          expect(result.errors).toContain(error);
        });
      });
    });
  });

  describe('Meeting Model', () => {
    interface Meeting {
      id?: string;
      title: string;
      meeting_date: string;
      duration?: number;
      status: 'scheduled' | 'in_progress' | 'completed' | 'cancelled';
      meeting_url?: string;
      user_id: string;
      created_at?: string;
      updated_at?: string;
    }

    const validateMeeting = (meeting: Partial<Meeting>): { isValid: boolean; errors: string[] } => {
      const errors: string[] = [];

      if (!meeting.title) {
        errors.push('Meeting title is required');
      }

      if (!meeting.meeting_date) {
        errors.push('Meeting date is required');
      } else {
        const date = new Date(meeting.meeting_date);
        if (isNaN(date.getTime())) {
          errors.push('Invalid meeting date format');
        }
      }

      if (!meeting.user_id) {
        errors.push('User ID is required');
      }

      if (meeting.status && !['scheduled', 'in_progress', 'completed', 'cancelled'].includes(meeting.status)) {
        errors.push('Invalid meeting status');
      }

      if (meeting.duration !== undefined && (meeting.duration <= 0 || isNaN(meeting.duration))) {
        errors.push('Duration must be a positive number');
      }

      return { isValid: errors.length === 0, errors };
    };

    it('should validate correct meeting data', () => {
      const validMeetings = [
        {
          title: 'Team Standup',
          meeting_date: '2024-01-15T10:00:00Z',
          status: 'scheduled' as const,
          user_id: 'user-123',
          duration: 30
        },
        {
          title: 'Client Review',
          meeting_date: '2024-01-16',
          status: 'completed' as const,
          user_id: 'user-456'
        }
      ];

      validMeetings.forEach(meeting => {
        const result = validateMeeting(meeting);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('should reject invalid meeting data', () => {
      const invalidMeetings = [
        { 
          data: { title: 'Test' }, 
          expectedErrors: ['Meeting date is required', 'User ID is required'] 
        },
        { 
          data: { title: 'Test', meeting_date: 'invalid-date', user_id: 'user-123' }, 
          expectedErrors: ['Invalid meeting date format'] 
        },
        { 
          data: { title: 'Test', meeting_date: '2024-01-15', user_id: 'user-123', duration: -30 }, 
          expectedErrors: ['Duration must be a positive number'] 
        }
      ];

      invalidMeetings.forEach(({ data, expectedErrors }) => {
        const result = validateMeeting(data);
        expect(result.isValid).toBe(false);
        expectedErrors.forEach(error => {
          expect(result.errors).toContain(error);
        });
      });
    });
  });

  describe('Task Model', () => {
    interface Task {
      id?: string;
      title: string;
      description?: string;
      status: 'todo' | 'in_progress' | 'completed' | 'cancelled';
      priority: 'low' | 'medium' | 'high';
      due_date?: string;
      project_id?: string;
      user_id: string;
      created_at?: string;
      updated_at?: string;
    }

    const validateTask = (task: Partial<Task>): { isValid: boolean; errors: string[] } => {
      const errors: string[] = [];

      if (!task.title) {
        errors.push('Task title is required');
      }

      if (!task.user_id) {
        errors.push('User ID is required');
      }

      if (task.status && !['todo', 'in_progress', 'completed', 'cancelled'].includes(task.status)) {
        errors.push('Invalid task status');
      }

      if (task.priority && !['low', 'medium', 'high'].includes(task.priority)) {
        errors.push('Invalid task priority');
      }

      if (task.due_date) {
        const date = new Date(task.due_date);
        if (isNaN(date.getTime())) {
          errors.push('Invalid due date format');
        }
      }

      return { isValid: errors.length === 0, errors };
    };

    it('should validate correct task data', () => {
      const validTasks = [
        {
          title: 'Complete feature',
          status: 'todo' as const,
          priority: 'high' as const,
          user_id: 'user-123',
          due_date: '2024-01-20'
        },
        {
          title: 'Review code',
          status: 'in_progress' as const,
          priority: 'medium' as const,
          user_id: 'user-456'
        }
      ];

      validTasks.forEach(task => {
        const result = validateTask(task);
        expect(result.isValid).toBe(true);
        expect(result.errors).toHaveLength(0);
      });
    });

    it('should reject invalid task data', () => {
      const invalidTasks = [
        { 
          data: { title: 'Test' }, 
          expectedErrors: ['User ID is required'] 
        },
        {
          data: { title: 'Test', user_id: 'user-123', status: 'invalid' as any },
          expectedErrors: ['Invalid task status']
        },
        {
          data: { title: 'Test', user_id: 'user-123', priority: 'urgent' as any },
          expectedErrors: ['Invalid task priority']
        },
        { 
          data: { title: 'Test', user_id: 'user-123', due_date: 'invalid-date' }, 
          expectedErrors: ['Invalid due date format'] 
        }
      ];

      invalidTasks.forEach(({ data, expectedErrors }) => {
        const result = validateTask(data);
        expect(result.isValid).toBe(false);
        expectedErrors.forEach(error => {
          expect(result.errors).toContain(error);
        });
      });
    });
  });
});
