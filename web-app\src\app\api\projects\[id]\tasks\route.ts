import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const { data, error } = await supabase
      .from('project_tasks')
      .select('*')
      .eq('project_id', params.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching project tasks:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch tasks' },
        { status: 500 }
      );
    }

    return NextResponse.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error in project tasks GET:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Creating task for project:', params.id);
    const body = await request.json();
    console.log('Request body:', body);

    const {
      title,
      description,
      status = 'todo',
      priority = 'medium',
      due_date,
      assigned_to
    } = body;

    // Validate required fields
    if (!title) {
      console.log('Validation failed: Title is required');
      return NextResponse.json(
        { success: false, error: 'Title is required' },
        { status: 400 }
      );
    }

    // For now, we'll get the user_id from the project
    // In production, you'd extract this from the JWT token
    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', params.id)
      .single();

    const userId = project?.user_id || 'user-placeholder';

    // Handle assigned_to field - if it's not a valid UUID, set to null or current user
    let assignedToId = null;
    if (assigned_to) {
      // Check if assigned_to is a valid UUID format
      const uuidRegex = /^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$/i;
      if (uuidRegex.test(assigned_to)) {
        assignedToId = assigned_to;
      } else {
        // If it's a name/string, assign to the current user
        assignedToId = userId;
      }
    }

    const taskData = {
      project_id: params.id,
      user_id: userId,
      title,
      description: description || '',
      status,
      priority,
      due_date: due_date || null,
      assigned_to: assignedToId,
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    console.log('Inserting task data:', taskData);

    const { data, error } = await supabase
      .from('project_tasks')
      .insert([taskData])
      .select('*')
      .single();

    if (error) {
      console.error('Error creating project task:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create task', details: error.message },
        { status: 500 }
      );
    }

    console.log('Task created successfully:', data);

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in project tasks POST:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error' },
      { status: 500 }
    );
  }
}
