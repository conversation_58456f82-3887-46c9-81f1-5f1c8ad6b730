import OpenAI from 'openai';

// Initialize OpenAI client
const openai = new OpenAI({
  apiKey: process.env.NEXT_PUBLIC_OPENAI_API_KEY || '',
  dangerouslyAllowBrowser: true // For client-side usage
});

export interface AITaskGenerationRequest {
  projectDescription: string;
  files?: File[];
  additionalContext?: string;
}

export interface AIDocumentGenerationRequest {
  documentType: string;
  projectData: {
    name: string;
    client: string;
    meetings: any[];
    tasks: any[];
    documents: any[];
    invoices: any[];
    budget?: number;
    deadline?: string;
  };
  dataSources: string[];
  customPrompt?: string;
}

export interface AIChatRequest {
  message: string;
  currentDocument: string;
  context: string;
}

// AI Task Generation
export async function generateTasksWithAI(request: AITaskGenerationRequest) {
  try {
    const prompt = `
You are an expert project manager. Based on the following project information, generate a comprehensive list of tasks for a freelance project.

Project Description: ${request.projectDescription}
${request.additionalContext ? `Additional Context: ${request.additionalContext}` : ''}

Please generate 5-8 realistic, actionable tasks that would be typical for this type of project. For each task, provide:
- Title (concise and clear)
- Description (detailed explanation of what needs to be done)
- Priority (high, medium, or low)
- Estimated duration in days
- Suggested assignee role (e.g., Developer, Designer, Project Manager, etc.)

Format the response as a JSON array of tasks with the following structure:
{
  "tasks": [
    {
      "title": "Task Title",
      "description": "Detailed description of the task",
      "priority": "high|medium|low",
      "estimatedDays": number,
      "assigneeRole": "Role Name"
    }
  ]
}

Make sure the tasks are realistic, well-structured, and appropriate for a freelance project.
`;

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are an expert project manager who creates detailed, realistic project tasks for freelancers. Always respond with valid JSON."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    });

    const response = completion.choices[0]?.message?.content;
    if (!response) {
      throw new Error('No response from OpenAI');
    }

    // Parse the JSON response
    const parsedResponse = JSON.parse(response);
    return parsedResponse.tasks;

  } catch (error) {
    console.error('Error generating tasks with AI:', error);
    throw new Error('Failed to generate tasks with AI');
  }
}

// AI Document Generation
export async function generateDocumentWithAI(request: AIDocumentGenerationRequest) {
  try {
    const { documentType, projectData, dataSources, customPrompt } = request;

    // Build context based on selected data sources
    let context = `Project: ${projectData.name}\nClient: ${projectData.client}\n`;
    
    if (projectData.budget) {
      context += `Budget: $${projectData.budget.toLocaleString()}\n`;
    }
    
    if (projectData.deadline) {
      context += `Deadline: ${new Date(projectData.deadline).toLocaleDateString()}\n`;
    }

    if (dataSources.includes('meetings') && projectData.meetings.length > 0) {
      context += `\nMeetings (${projectData.meetings.length}):\n`;
      projectData.meetings.forEach(meeting => {
        context += `- ${meeting.title} (${new Date(meeting.recorded_at).toLocaleDateString()})\n`;
      });
    }

    if (dataSources.includes('tasks') && projectData.tasks.length > 0) {
      context += `\nTasks (${projectData.tasks.length}):\n`;
      projectData.tasks.forEach(task => {
        context += `- ${task.title} (${task.status}) - ${task.priority} priority\n`;
      });
    }

    if (dataSources.includes('documents') && projectData.documents.length > 0) {
      context += `\nDocuments (${projectData.documents.length}):\n`;
      projectData.documents.forEach(doc => {
        context += `- ${doc.name} (${doc.file_type})\n`;
      });
    }

    if (dataSources.includes('invoices') && projectData.invoices.length > 0) {
      context += `\nInvoices (${projectData.invoices.length}):\n`;
      projectData.invoices.forEach(invoice => {
        context += `- ${invoice.invoice_number}: $${invoice.amount} (${invoice.status})\n`;
      });
    }

    const prompt = `
You are a professional business document writer. Create a comprehensive ${documentType} document based on the following project information:

${context}

${customPrompt ? `Additional Requirements: ${customPrompt}` : ''}

Please create a professional, well-structured document that includes:
- Proper headings and sections
- Professional language and tone
- Relevant details based on the available project data
- Appropriate formatting using Markdown
- A professional conclusion

The document should be suitable for client presentation and business use.
`;

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: `You are a professional business document writer specializing in ${documentType} documents. Create comprehensive, well-structured documents using proper Markdown formatting.`
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 3000
    });

    const response = completion.choices[0]?.message?.content;
    if (!response) {
      throw new Error('No response from OpenAI');
    }

    return response;

  } catch (error) {
    console.error('Error generating document with AI:', error);
    throw new Error('Failed to generate document with AI');
  }
}

// AI Chat for Document Improvement
export async function chatWithAI(request: AIChatRequest) {
  try {
    const { message, currentDocument, context } = request;

    const prompt = `
You are an AI assistant helping to improve a business document. 

Current Document:
${currentDocument}

Context: ${context}

User Request: ${message}

Please provide a helpful response and if the user is asking to modify the document, provide the improved version. If you're modifying the document, return the complete updated document with the requested changes.

Be professional, helpful, and focus on improving the document quality.
`;

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a professional business document assistant. Help users improve their documents with expert advice and modifications."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2000
    });

    const response = completion.choices[0]?.message?.content;
    if (!response) {
      throw new Error('No response from OpenAI');
    }

    return response;

  } catch (error) {
    console.error('Error in AI chat:', error);
    throw new Error('Failed to process AI chat request');
  }
}

// Email Content Generation
export async function generateEmailContent(type: string, data: any) {
  try {
    let prompt = '';

    switch (type) {
      case 'meeting_summary':
        prompt = `
Generate a professional email to send meeting summary to client.

Meeting Details:
- Title: ${data.title}
- Date: ${data.date}
- Duration: ${data.duration}
- Summary: ${data.summary}
- Action Items: ${data.actionItems?.join(', ') || 'None'}

Create a professional email with subject and body that includes the meeting summary and next steps.
`;
        break;

      case 'task_update':
        prompt = `
Generate a professional email to update client about task progress.

Task Details:
- Title: ${data.title}
- Status: ${data.status}
- Progress: ${data.progress}
- Next Steps: ${data.nextSteps}

Create a professional email with subject and body that updates the client on task progress.
`;
        break;

      case 'project_update':
        prompt = `
Generate a professional project update email for client.

Project Details:
- Name: ${data.projectName}
- Progress: ${data.progress}
- Completed Tasks: ${data.completedTasks}
- Upcoming Milestones: ${data.upcomingMilestones}

Create a comprehensive project update email with subject and body.
`;
        break;

      default:
        throw new Error('Unknown email type');
    }

    const completion = await openai.chat.completions.create({
      model: "gpt-4",
      messages: [
        {
          role: "system",
          content: "You are a professional business communication specialist. Generate clear, professional emails for client communication."
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 1000
    });

    const response = completion.choices[0]?.message?.content;
    if (!response) {
      throw new Error('No response from OpenAI');
    }

    return response;

  } catch (error) {
    console.error('Error generating email content:', error);
    throw new Error('Failed to generate email content');
  }
}

export default openai;
