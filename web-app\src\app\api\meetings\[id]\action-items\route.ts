import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Fetching action items for meeting:', params.id);

    const { data, error } = await supabase
      .from('action_items')
      .select('*')
      .eq('meeting_id', params.id)
      .order('created_at', { ascending: false });

    if (error) {
      console.error('Error fetching action items:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch action items', details: error.message },
        { status: 500 }
      );
    }

    console.log('Action items fetched successfully:', data?.length || 0, 'items');

    return NextResponse.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error in action items GET:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    console.log('Creating action item for meeting:', params.id, body);

    const {
      description,
      assigned_to,
      due_date,
      priority = 'medium',
      status = 'pending'
    } = body;

    // Validate required fields
    if (!description) {
      return NextResponse.json(
        { success: false, error: 'Description is required' },
        { status: 400 }
      );
    }

    // Get user_id for the action item (required field)
    const { data: meeting } = await supabase
      .from('meetings')
      .select('user_id')
      .eq('id', params.id)
      .single();

    if (!meeting?.user_id) {
      return NextResponse.json(
        { success: false, error: 'Meeting not found or missing user' },
        { status: 404 }
      );
    }

    // Create action item (using 'task' field instead of 'description' based on schema)
    const { data, error } = await supabase
      .from('action_items')
      .insert({
        meeting_id: params.id,
        user_id: meeting.user_id,
        task: description, // Using 'task' field from current schema
        priority,
        status,
        created_at: new Date().toISOString()
      })
      .select()
      .single();

    if (error) {
      console.error('Error creating action item:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create action item', details: error.message },
        { status: 500 }
      );
    }

    console.log('Action item created successfully:', data);

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in action items POST:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
