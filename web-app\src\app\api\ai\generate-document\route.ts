import { NextRequest, NextResponse } from 'next/server';
import OpenAI from 'openai';

const openai = new OpenAI({
  apiKey: process.env.OPENAI_API_KEY,
});

export async function POST(request: NextRequest) {
  try {
    const { documentType, projectData, dataSources, customPrompt } = await request.json();

    if (!documentType || !projectData) {
      return NextResponse.json(
        { error: 'Document type and project data are required' },
        { status: 400 }
      );
    }

    // Build context based on selected data sources
    let context = `Project: ${projectData.name}\nClient: ${projectData.client}\n`;
    
    if (projectData.budget) {
      context += `Budget: $${projectData.budget.toLocaleString()}\n`;
    }
    
    if (projectData.deadline) {
      context += `Deadline: ${new Date(projectData.deadline).toLocaleDateString()}\n`;
    }

    if (dataSources.includes('meetings') && projectData.meetings.length > 0) {
      context += `\nMeetings (${projectData.meetings.length}):\n`;
      projectData.meetings.forEach((meeting: any) => {
        context += `- ${meeting.title} (${new Date(meeting.recorded_at).toLocaleDateString()})\n`;
      });
    }

    if (dataSources.includes('tasks') && projectData.tasks.length > 0) {
      context += `\nTasks (${projectData.tasks.length}):\n`;
      projectData.tasks.forEach((task: any) => {
        context += `- ${task.title} (${task.status}) - ${task.priority} priority\n`;
      });
    }

    if (dataSources.includes('documents') && projectData.documents.length > 0) {
      context += `\nDocuments (${projectData.documents.length}):\n`;
      projectData.documents.forEach((doc: any) => {
        context += `- ${doc.name} (${doc.file_type})\n`;
      });
    }

    if (dataSources.includes('invoices') && projectData.invoices.length > 0) {
      context += `\nInvoices (${projectData.invoices.length}):\n`;
      projectData.invoices.forEach((invoice: any) => {
        context += `- ${invoice.invoice_number}: $${invoice.amount} (${invoice.status})\n`;
      });
    }

    const prompt = `
You are a professional business document writer. Create a comprehensive ${documentType} document based on the following project information:

${context}

${customPrompt ? `Additional Requirements: ${customPrompt}` : ''}

Please create a professional, well-structured document that includes:
- Proper headings and sections
- Professional language and tone
- Relevant details based on the available project data
- Appropriate formatting using Markdown
- A professional conclusion

The document should be suitable for client presentation and business use.
`;

    const completion = await openai.chat.completions.create({
      model: "gpt-3.5-turbo",
      messages: [
        {
          role: "system",
          content: `You are a professional business document writer specializing in ${documentType} documents. Create comprehensive, well-structured documents using proper Markdown formatting.`
        },
        {
          role: "user",
          content: prompt
        }
      ],
      temperature: 0.7,
      max_tokens: 2500
    });

    const response = completion.choices[0]?.message?.content;
    if (!response) {
      throw new Error('No response from OpenAI');
    }

    return NextResponse.json({ document: response });

  } catch (error) {
    console.error('Error generating document with AI:', error);
    
    // Return fallback document if OpenAI fails
    const fallbackDocument = `# ${documentType.replace('-', ' ').replace(/\b\w/g, l => l.toUpperCase())}

## Project Overview
**Project:** ${projectData?.name || 'Project Name'}
**Client:** ${projectData?.client || 'Client Name'}
**Date:** ${new Date().toLocaleDateString()}

## Executive Summary
This document provides a comprehensive overview of the project requirements, scope, and deliverables. The project aims to deliver high-quality results that meet client expectations and business objectives.

## Project Details
- **Timeline:** Based on project requirements and scope
- **Budget:** As per agreed terms and conditions
- **Deliverables:** Professional implementation of all specified requirements

## Next Steps
1. Review and approve project specifications
2. Begin implementation phase
3. Regular progress updates and client communication
4. Quality assurance and testing
5. Final delivery and project closure

---
*This document was prepared using KaiNote AI on ${new Date().toLocaleDateString()}*`;

    return NextResponse.json({ 
      document: fallbackDocument,
      fallback: true,
      error: 'OpenAI unavailable, using fallback document'
    });
  }
}
