import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Fetching meeting details for ID:', params.id);

    const { data, error } = await supabase
      .from('meetings')
      .select(`
        *,
        project:projects(
          id,
          name,
          client_name,
          client_email
        )
      `)
      .eq('id', params.id)
      .single();

    if (error) {
      console.error('Error fetching meeting:', error);
      return NextResponse.json(
        { success: false, error: 'Meeting not found', details: error.message },
        { status: 404 }
      );
    }

    // Parse AI content from meeting_url if it contains JSON
    let parsedData = { ...data };
    if (data.meeting_url && data.meeting_url.startsWith('{')) {
      try {
        const aiContent = JSON.parse(data.meeting_url);
        parsedData = {
          ...data,
          transcript: aiContent.transcript,
          summary: aiContent.summary,
          client_summary: aiContent.client_summary,
          processed_at: aiContent.processed_at,
          original_file_url: aiContent.original_file_url // Preserve original file URL
        };
      } catch (error) {
        console.log('Could not parse AI content from meeting_url');
      }
    }

    console.log('Meeting fetched successfully:', parsedData);

    return NextResponse.json({
      success: true,
      data: parsedData
    });

  } catch (error) {
    console.error('Error in meeting GET:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function PUT(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    console.log('Updating meeting:', params.id, body);

    const { data, error } = await supabase
      .from('meetings')
      .update(body)
      .eq('id', params.id)
      .select()
      .single();

    if (error) {
      console.error('Error updating meeting:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to update meeting', details: error.message },
        { status: 500 }
      );
    }

    console.log('Meeting updated successfully:', data);

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in meeting PUT:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function DELETE(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Deleting meeting:', params.id);

    const { error } = await supabase
      .from('meetings')
      .delete()
      .eq('id', params.id);

    if (error) {
      console.error('Error deleting meeting:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to delete meeting', details: error.message },
        { status: 500 }
      );
    }

    console.log('Meeting deleted successfully');

    return NextResponse.json({
      success: true,
      message: 'Meeting deleted successfully'
    });

  } catch (error) {
    console.error('Error in meeting DELETE:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
