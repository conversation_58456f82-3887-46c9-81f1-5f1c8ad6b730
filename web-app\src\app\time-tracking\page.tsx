'use client';

import { useState, useEffect } from 'react';
import { useRouter } from 'next/navigation';
import { useQuery, useMutation, useQueryClient } from 'react-query';
import { useAuth } from '@/lib/auth';
import { DashboardLayout } from '@/components/layout/DashboardLayout';
import { apiHelpers } from '@/lib/api';
import { 
  PlayIcon, 
  StopIcon, 
  ClockIcon,
  PlusIcon,
  CalendarIcon,
  CurrencyDollarIcon,
  ChartBarIcon
} from '@heroicons/react/24/outline';
import { formatDistanceToNow, format } from 'date-fns';

interface TimeEntry {
  id: string;
  project_id: string;
  task_id?: string;
  meeting_id?: string;
  description?: string;
  start_time: string;
  end_time?: string;
  duration_minutes?: number;
  hourly_rate: number;
  is_billable: boolean;
  is_running: boolean;
  tags: string[];
  project?: {
    id: string;
    name: string;
    client_name: string;
  };
  task?: {
    id: string;
    title: string;
  };
}

interface ActiveTimer extends TimeEntry {}

export default function TimeTrackingPage() {
  const { isAuthenticated, isLoading: authLoading } = useAuth();
  const router = useRouter();
  const queryClient = useQueryClient();
  const [selectedPeriod, setSelectedPeriod] = useState<'week' | 'month' | 'quarter' | 'year'>('week');
  const [showNewEntryForm, setShowNewEntryForm] = useState(false);

  useEffect(() => {
    if (!authLoading && !isAuthenticated) {
      router.push('/auth/signin');
    }
  }, [isAuthenticated, authLoading, router]);

  // Fetch active timer
  const { data: activeTimer, refetch: refetchActiveTimer } = useQuery<{ data: ActiveTimer | null }>(
    'active-timer',
    apiHelpers.getActiveTimer,
    {
      enabled: isAuthenticated,
      refetchInterval: 1000, // Update every second for live timer
    }
  );

  // Fetch time entries
  const { data: timeEntriesResponse, isLoading: entriesLoading } = useQuery(
    ['time-entries', selectedPeriod],
    () => apiHelpers.getTimeEntries({ limit: 50 }),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch analytics
  const { data: analyticsResponse } = useQuery(
    ['time-analytics', selectedPeriod],
    () => apiHelpers.getTimeAnalytics({ period: selectedPeriod }),
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Fetch projects for timer
  const { data: projectsResponse } = useQuery(
    'projects',
    apiHelpers.getProjects,
    {
      enabled: isAuthenticated,
      select: (response) => response.data.data,
    }
  );

  // Stop timer mutation
  const stopTimerMutation = useMutation(
    (data: { description?: string }) => apiHelpers.stopTimer(data),
    {
      onSuccess: () => {
        queryClient.invalidateQueries('active-timer');
        queryClient.invalidateQueries('time-entries');
        queryClient.invalidateQueries('time-analytics');
      },
    }
  );

  const handleStopTimer = async (description?: string) => {
    try {
      await stopTimerMutation.mutateAsync({ description });
    } catch (error) {
      console.error('Error stopping timer:', error);
    }
  };

  // Calculate elapsed time for active timer
  const getElapsedTime = (startTime: string) => {
    const start = new Date(startTime);
    const now = new Date();
    const diffMs = now.getTime() - start.getTime();
    const hours = Math.floor(diffMs / (1000 * 60 * 60));
    const minutes = Math.floor((diffMs % (1000 * 60 * 60)) / (1000 * 60));
    const seconds = Math.floor((diffMs % (1000 * 60)) / 1000);
    return `${hours.toString().padStart(2, '0')}:${minutes.toString().padStart(2, '0')}:${seconds.toString().padStart(2, '0')}`;
  };

  // Format duration
  const formatDuration = (minutes: number) => {
    const hours = Math.floor(minutes / 60);
    const mins = minutes % 60;
    return `${hours}h ${mins}m`;
  };

  if (authLoading || !isAuthenticated) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600"></div>
      </div>
    );
  }

  const timeEntries = Array.isArray(timeEntriesResponse) ? timeEntriesResponse : [];
  const analytics = analyticsResponse?.summary;
  const projects = Array.isArray(projectsResponse) ? projectsResponse : [];

  return (
    <DashboardLayout>
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        {/* Header */}
        <div className="mb-8">
          <div className="flex items-center justify-between">
            <div>
              <h1 className="text-2xl font-bold text-gray-900">Time Tracking</h1>
              <p className="text-gray-600">Track your time and boost productivity</p>
            </div>
            <div className="flex items-center space-x-4">
              <select
                value={selectedPeriod}
                onChange={(e) => setSelectedPeriod(e.target.value as any)}
                className="rounded-md border-gray-300 shadow-sm focus:border-primary-500 focus:ring-primary-500"
              >
                <option value="week">This Week</option>
                <option value="month">This Month</option>
                <option value="quarter">This Quarter</option>
                <option value="year">This Year</option>
              </select>
              <button
                onClick={() => setShowNewEntryForm(true)}
                className="bg-primary-600 text-white px-4 py-2 rounded-lg hover:bg-primary-700 flex items-center space-x-2"
              >
                <PlusIcon className="h-5 w-5" />
                <span>Add Entry</span>
              </button>
            </div>
          </div>
        </div>

        {/* Active Timer */}
        {activeTimer?.data && (
          <div className="bg-gradient-to-r from-green-500 to-blue-600 rounded-lg p-6 mb-8 text-white">
            <div className="flex items-center justify-between">
              <div className="flex items-center space-x-4">
                <div className="bg-white bg-opacity-20 rounded-full p-3">
                  <ClockIcon className="h-6 w-6" />
                </div>
                <div>
                  <h3 className="text-lg font-semibold">Timer Running</h3>
                  <p className="text-green-100">
                    {activeTimer.data.project?.name} - {activeTimer.data.project?.client_name}
                  </p>
                  {activeTimer.data.description && (
                    <p className="text-green-100 text-sm">{activeTimer.data.description}</p>
                  )}
                </div>
              </div>
              <div className="text-right">
                <div className="text-3xl font-mono font-bold">
                  {getElapsedTime(activeTimer.data.start_time)}
                </div>
                <button
                  onClick={() => handleStopTimer()}
                  className="mt-2 bg-white bg-opacity-20 text-white px-4 py-2 rounded-lg hover:bg-opacity-30 flex items-center space-x-2"
                >
                  <StopIcon className="h-4 w-4" />
                  <span>Stop Timer</span>
                </button>
              </div>
            </div>
          </div>
        )}

        {/* Analytics Cards */}
        {analytics && (
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-8">
            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-blue-100 rounded-lg p-3">
                  <ClockIcon className="h-6 w-6 text-blue-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Total Hours</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.totalHours}h</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-green-100 rounded-lg p-3">
                  <CurrencyDollarIcon className="h-6 w-6 text-green-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Billable Hours</p>
                  <p className="text-2xl font-bold text-gray-900">{analytics.billableHours}h</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-purple-100 rounded-lg p-3">
                  <ChartBarIcon className="h-6 w-6 text-purple-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Revenue</p>
                  <p className="text-2xl font-bold text-gray-900">${analytics.totalRevenue}</p>
                </div>
              </div>
            </div>

            <div className="bg-white rounded-lg shadow p-6">
              <div className="flex items-center">
                <div className="bg-orange-100 rounded-lg p-3">
                  <CalendarIcon className="h-6 w-6 text-orange-600" />
                </div>
                <div className="ml-4">
                  <p className="text-sm font-medium text-gray-600">Avg Rate</p>
                  <p className="text-2xl font-bold text-gray-900">${analytics.averageHourlyRate}/h</p>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Time Entries List */}
        <div className="bg-white rounded-lg shadow">
          <div className="px-6 py-4 border-b border-gray-200">
            <h2 className="text-lg font-semibold text-gray-900">Recent Time Entries</h2>
          </div>
          
          {entriesLoading ? (
            <div className="p-6 text-center">
              <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary-600 mx-auto"></div>
            </div>
          ) : timeEntries.length === 0 ? (
            <div className="p-6 text-center">
              <ClockIcon className="mx-auto h-12 w-12 text-gray-400" />
              <h3 className="mt-2 text-sm font-medium text-gray-900">No time entries yet</h3>
              <p className="mt-1 text-sm text-gray-500">Start tracking your time to see entries here.</p>
            </div>
          ) : (
            <div className="divide-y divide-gray-200">
              {timeEntries.map((entry: TimeEntry) => (
                <div key={entry.id} className="p-6 hover:bg-gray-50">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <div className="flex items-center space-x-3">
                        <div className={`w-3 h-3 rounded-full ${entry.is_billable ? 'bg-green-500' : 'bg-gray-400'}`}></div>
                        <div>
                          <p className="text-sm font-medium text-gray-900">
                            {entry.project?.name} - {entry.project?.client_name}
                          </p>
                          {entry.description && (
                            <p className="text-sm text-gray-600">{entry.description}</p>
                          )}
                          {entry.task && (
                            <p className="text-xs text-gray-500">Task: {entry.task.title}</p>
                          )}
                        </div>
                      </div>
                    </div>
                    
                    <div className="flex items-center space-x-6 text-sm text-gray-500">
                      <div>
                        <p>{format(new Date(entry.start_time), 'MMM d, yyyy')}</p>
                        <p>{format(new Date(entry.start_time), 'h:mm a')} - {entry.end_time ? format(new Date(entry.end_time), 'h:mm a') : 'Running'}</p>
                      </div>
                      
                      <div className="text-right">
                        <p className="font-medium text-gray-900">
                          {entry.duration_minutes ? formatDuration(entry.duration_minutes) : 'Running'}
                        </p>
                        {entry.is_billable && entry.duration_minutes && (
                          <p className="text-green-600">
                            ${((entry.duration_minutes / 60) * entry.hourly_rate).toFixed(2)}
                          </p>
                        )}
                      </div>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      </div>
    </DashboardLayout>
  );
}
