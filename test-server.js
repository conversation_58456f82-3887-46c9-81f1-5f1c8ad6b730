const http = require('http');

const server = http.createServer((req, res) => {
  res.writeHead(200, { 'Content-Type': 'text/html' });
  res.end(`
    <html>
      <body>
        <h1>🎉 Test Server Working!</h1>
        <p>If you can see this, your Node.js server is working correctly.</p>
        <p>Time: ${new Date().toLocaleString()}</p>
      </body>
    </html>
  `);
});

const PORT = 3005;
server.listen(PORT, '0.0.0.0', () => {
  console.log(`🧪 Test server running on http://localhost:${PORT}`);
  console.log(`🧪 Also try: http://127.0.0.1:${PORT}`);
});
