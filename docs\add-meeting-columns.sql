-- Add missing columns to meetings table for transcript and AI-generated content
-- Run this in your Supabase SQL editor

-- Add transcript_url column for uploaded transcript files
ALTER TABLE public.meetings 
ADD COLUMN IF NOT EXISTS transcript_url TEXT;

-- Add transcript column for the actual transcript text
ALTER TABLE public.meetings 
ADD COLUMN IF NOT EXISTS transcript TEXT;

-- Add summary column for AI-generated meeting summary
ALTER TABLE public.meetings 
ADD COLUMN IF NOT EXISTS summary TEXT;

-- Add client_summary column for client-facing summary
ALTER TABLE public.meetings 
ADD COLUMN IF NOT EXISTS client_summary TEXT;

-- Add shareable_token for public meeting links
ALTER TABLE public.meetings 
ADD COLUMN IF NOT EXISTS shareable_token TEXT UNIQUE;

-- Add shareable_expires_at for token expiration
ALTER TABLE public.meetings 
ADD COLUMN IF NOT EXISTS shareable_expires_at TIMESTAMP WITH TIME ZONE;

-- Update the platform check constraint to include more platforms
ALTER TABLE public.meetings 
DROP CONSTRAINT IF EXISTS meetings_platform_check;

ALTER TABLE public.meetings 
ADD CONSTRAINT meetings_platform_check 
CHECK (platform IN ('google-meet', 'zoom', 'teams', 'webex', 'in-person', 'phone', 'other', 'upload'));

-- Create index on shareable_token for faster lookups
CREATE INDEX IF NOT EXISTS idx_meetings_shareable_token ON public.meetings(shareable_token);

-- Create index on project_id for faster project meeting queries
CREATE INDEX IF NOT EXISTS idx_meetings_project_id ON public.meetings(project_id);

-- Create index on user_id for faster user meeting queries
CREATE INDEX IF NOT EXISTS idx_meetings_user_id ON public.meetings(user_id);

-- Update action_items table to match the expected schema
ALTER TABLE public.action_items 
ADD COLUMN IF NOT EXISTS description TEXT;

-- If task column exists, copy it to description and drop task
DO $$
BEGIN
    IF EXISTS (SELECT 1 FROM information_schema.columns WHERE table_name = 'action_items' AND column_name = 'task') THEN
        UPDATE public.action_items SET description = task WHERE description IS NULL;
        ALTER TABLE public.action_items DROP COLUMN task;
    END IF;
END $$;

-- Add assigned_to column if it doesn't exist
ALTER TABLE public.action_items 
ADD COLUMN IF NOT EXISTS assigned_to TEXT;

-- Add due_date column if it doesn't exist (different from deadline)
ALTER TABLE public.action_items 
ADD COLUMN IF NOT EXISTS due_date DATE;

-- Update status check constraint to include more statuses
ALTER TABLE public.action_items 
DROP CONSTRAINT IF EXISTS action_items_status_check;

ALTER TABLE public.action_items 
ADD CONSTRAINT action_items_status_check 
CHECK (status IN ('pending', 'in_progress', 'completed', 'cancelled'));

-- Create a view for meeting details with related data
CREATE OR REPLACE VIEW public.meeting_details AS
SELECT 
    m.*,
    p.name as project_name,
    p.client_name,
    p.client_email,
    COUNT(ai.id) as action_items_count
FROM public.meetings m
LEFT JOIN public.projects p ON m.project_id = p.id
LEFT JOIN public.action_items ai ON m.id = ai.meeting_id
GROUP BY m.id, p.name, p.client_name, p.client_email;

-- Grant necessary permissions
GRANT SELECT, INSERT, UPDATE, DELETE ON public.meetings TO authenticated;
GRANT SELECT, INSERT, UPDATE, DELETE ON public.action_items TO authenticated;
GRANT SELECT ON public.meeting_details TO authenticated;

-- Enable RLS (Row Level Security) if not already enabled
ALTER TABLE public.meetings ENABLE ROW LEVEL SECURITY;
ALTER TABLE public.action_items ENABLE ROW LEVEL SECURITY;

-- Create RLS policies for meetings
DROP POLICY IF EXISTS "Users can view their own meetings" ON public.meetings;
CREATE POLICY "Users can view their own meetings" ON public.meetings
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own meetings" ON public.meetings;
CREATE POLICY "Users can insert their own meetings" ON public.meetings
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own meetings" ON public.meetings;
CREATE POLICY "Users can update their own meetings" ON public.meetings
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own meetings" ON public.meetings;
CREATE POLICY "Users can delete their own meetings" ON public.meetings
    FOR DELETE USING (auth.uid() = user_id);

-- Create RLS policies for action_items
DROP POLICY IF EXISTS "Users can view their own action items" ON public.action_items;
CREATE POLICY "Users can view their own action items" ON public.action_items
    FOR SELECT USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can insert their own action items" ON public.action_items;
CREATE POLICY "Users can insert their own action items" ON public.action_items
    FOR INSERT WITH CHECK (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can update their own action items" ON public.action_items;
CREATE POLICY "Users can update their own action items" ON public.action_items
    FOR UPDATE USING (auth.uid() = user_id);

DROP POLICY IF EXISTS "Users can delete their own action items" ON public.action_items;
CREATE POLICY "Users can delete their own action items" ON public.action_items
    FOR DELETE USING (auth.uid() = user_id);

-- Add comment for documentation
COMMENT ON COLUMN public.meetings.transcript_url IS 'URL to uploaded transcript file';
COMMENT ON COLUMN public.meetings.transcript IS 'Full meeting transcript text';
COMMENT ON COLUMN public.meetings.summary IS 'AI-generated meeting summary';
COMMENT ON COLUMN public.meetings.client_summary IS 'Client-facing meeting summary';
COMMENT ON COLUMN public.meetings.shareable_token IS 'Token for public meeting access';
COMMENT ON COLUMN public.meetings.shareable_expires_at IS 'Expiration time for shareable link';
