# KaiNote Working State Backup Script
# Run this script to create a backup of the current working state

param(
    [string]$BackupPath = "C:\Users\<USER>\Desktop\kai-backups",
    [string]$BackupName = "kai-backup-$(Get-Date -Format 'yyyy-MM-dd-HHmm')"
)

Write-Host "Creating KaiNote Working State Backup..." -ForegroundColor Green
Write-Host "Backup Location: $BackupPath\$BackupName" -ForegroundColor Yellow

# Create backup directory
$FullBackupPath = Join-Path $BackupPath $BackupName
New-Item -ItemType Directory -Path $FullBackupPath -Force | Out-Null

# Define critical files and directories to backup
$CriticalPaths = @(
    # API Routes (Critical)
    "web-app\src\app\api\meetings\route.ts",
    "web-app\src\app\api\meetings\[id]\process\route.ts",
    "web-app\src\app\api\meetings\[id]\route.ts",
    "web-app\src\app\api\upload\route.ts",
    "web-app\src\app\api\projects\[id]\route.ts",
    
    # Frontend Pages (Important)
    "web-app\src\app\projects\[id]\page.tsx",
    "web-app\src\app\meetings\[id]\page.tsx",
    "web-app\src\app\dashboard\page.tsx",
    
    # Configuration Files
    "web-app\package.json",
    "web-app\.env.local",
    "web-app\next.config.js",
    "web-app\tailwind.config.js",
    
    # Documentation
    "docs\WORKING_STATE_BACKUP.md",
    "docs\database-schema.sql",
    "docs\setup-openai.md",
    
    # Database Files
    "docs\database-migration.sql",
    "docs\missing-tables.sql"
)

# Copy critical files
Write-Host "Backing up critical files..." -ForegroundColor Cyan
foreach ($Path in $CriticalPaths) {
    $SourcePath = Join-Path "C:\Users\<USER>\Desktop\kai" $Path
    if (Test-Path $SourcePath) {
        $DestPath = Join-Path $FullBackupPath $Path
        $DestDir = Split-Path $DestPath -Parent
        New-Item -ItemType Directory -Path $DestDir -Force | Out-Null
        Copy-Item $SourcePath $DestPath -Force
        Write-Host "  OK: $Path" -ForegroundColor Green
    } else {
        Write-Host "  SKIP: $Path (not found)" -ForegroundColor Yellow
    }
}

# Backup entire components directory
Write-Host "📦 Backing up UI components..." -ForegroundColor Cyan
$ComponentsSource = "C:\Users\<USER>\Desktop\kai\web-app\src\components"
$ComponentsDest = Join-Path $FullBackupPath "web-app\src\components"
if (Test-Path $ComponentsSource) {
    Copy-Item $ComponentsSource $ComponentsDest -Recurse -Force
    Write-Host "  ✅ Components directory backed up" -ForegroundColor Green
}

# Backup uploads directory (sample files)
Write-Host "📄 Backing up sample uploads..." -ForegroundColor Cyan
$UploadsSource = "C:\Users\<USER>\Desktop\kai\web-app\public\uploads"
$UploadsDest = Join-Path $FullBackupPath "web-app\public\uploads"
if (Test-Path $UploadsSource) {
    # Only backup a few sample files, not all uploads
    New-Item -ItemType Directory -Path $UploadsDest -Force | Out-Null
    $SampleFiles = Get-ChildItem $UploadsSource -Recurse -File | Select-Object -First 5
    foreach ($File in $SampleFiles) {
        $RelativePath = $File.FullName.Substring($UploadsSource.Length + 1)
        $DestFile = Join-Path $UploadsDest $RelativePath
        $DestFileDir = Split-Path $DestFile -Parent
        New-Item -ItemType Directory -Path $DestFileDir -Force | Out-Null
        Copy-Item $File.FullName $DestFile -Force
    }
    Write-Host "  ✅ Sample uploads backed up" -ForegroundColor Green
}

# Create backup manifest
Write-Host "📋 Creating backup manifest..." -ForegroundColor Cyan
$Manifest = @"
# KaiNote Working State Backup Manifest
Created: $(Get-Date -Format 'yyyy-MM-dd HH:mm:ss')
Backup Name: $BackupName
System Status: FULLY FUNCTIONAL

## What's Included:
- ✅ Critical API endpoints (meetings, upload, projects)
- ✅ Frontend pages and components
- ✅ Database schema and migration scripts
- ✅ Configuration files
- ✅ Documentation and setup guides
- ✅ Sample uploaded files

## Restore Instructions:
1. Copy files back to original locations
2. Run: npm install (in web-app directory)
3. Check database schema matches docs/database-schema.sql
4. Test meeting creation and file upload
5. Verify AI processing works

## Last Known Working Features:
- ✅ Meeting creation with platform validation
- ✅ PDF file upload and processing
- ✅ AI content generation (with fallbacks)
- ✅ Professional meeting summaries
- ✅ Client communication templates
- ✅ Project management interface
- ✅ Task management system
- ✅ Invoice management

## Critical Fixes Applied:
- Fixed platform validation in meetings API
- Stabilized PDF processing without external libraries
- Implemented robust AI processing with fallbacks
- Added comprehensive error handling

System is PRODUCTION READY! 🎉
"@

$Manifest | Out-File -FilePath (Join-Path $FullBackupPath "BACKUP_MANIFEST.md") -Encoding UTF8

# Create restore script
$RestoreScript = @"
# KaiNote Restore Script
# Run this to restore from backup

Write-Host "🔄 Restoring KaiNote from backup..." -ForegroundColor Green

# Copy files back to original location
`$BackupPath = `$PSScriptRoot
`$TargetPath = "C:\Users\<USER>\Desktop\kai"

Write-Host "Copying files from backup..." -ForegroundColor Cyan
Copy-Item "`$BackupPath\*" `$TargetPath -Recurse -Force

Write-Host "✅ Files restored successfully!" -ForegroundColor Green
Write-Host "Next steps:" -ForegroundColor Yellow
Write-Host "1. cd C:\Users\<USER>\Desktop\kai\web-app" -ForegroundColor White
Write-Host "2. npm install" -ForegroundColor White
Write-Host "3. npm run dev" -ForegroundColor White
Write-Host "4. Test meeting creation" -ForegroundColor White
"@

$RestoreScript | Out-File -FilePath (Join-Path $FullBackupPath "restore.ps1") -Encoding UTF8

# Create quick test script
$TestScript = @"
# Quick Test Script - Verify System is Working

Write-Host "🧪 Testing KaiNote System..." -ForegroundColor Green

# Test 1: Check if server is running
try {
    `$response = Invoke-WebRequest -Uri "http://localhost:3003" -Method HEAD -TimeoutSec 5
    Write-Host "✅ Server is running" -ForegroundColor Green
} catch {
    Write-Host "❌ Server is not running - run 'npm run dev' first" -ForegroundColor Red
    exit 1
}

# Test 2: Test API endpoint
try {
    `$projects = Invoke-RestMethod -Uri "http://localhost:3003/api/projects" -Method GET
    Write-Host "✅ API endpoints working" -ForegroundColor Green
} catch {
    Write-Host "❌ API endpoints not responding" -ForegroundColor Red
}

# Test 3: Check file upload directory
if (Test-Path "C:\Users\<USER>\Desktop\kai\web-app\public\uploads") {
    Write-Host "✅ Upload directory exists" -ForegroundColor Green
} else {
    Write-Host "⚠️  Upload directory missing - will be created automatically" -ForegroundColor Yellow
}

Write-Host "🎉 System test completed!" -ForegroundColor Green
Write-Host "Ready to create meetings and upload files!" -ForegroundColor Cyan
"@

$TestScript | Out-File -FilePath (Join-Path $FullBackupPath "test-system.ps1") -Encoding UTF8

# Completion message
Write-Host ""
Write-Host "🎉 Backup completed successfully!" -ForegroundColor Green
Write-Host "📁 Location: $FullBackupPath" -ForegroundColor Cyan
Write-Host ""
Write-Host "📋 Backup includes:" -ForegroundColor Yellow
Write-Host "  • All critical API files" -ForegroundColor White
Write-Host "  • Frontend components and pages" -ForegroundColor White
Write-Host "  • Database schema and migrations" -ForegroundColor White
Write-Host "  • Configuration files" -ForegroundColor White
Write-Host "  • Documentation and setup guides" -ForegroundColor White
Write-Host "  • Restore and test scripts" -ForegroundColor White
Write-Host ""
Write-Host "🔄 To restore later:" -ForegroundColor Yellow
Write-Host "  Run: $FullBackupPath\restore.ps1" -ForegroundColor White
Write-Host ""
Write-Host "🧪 To test system:" -ForegroundColor Yellow
Write-Host "  Run: $FullBackupPath\test-system.ps1" -ForegroundColor White
