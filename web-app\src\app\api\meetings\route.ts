import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(request: NextRequest) {
  try {
    const { searchParams } = new URL(request.url);
    const limit = searchParams.get('limit') || '50';

    console.log('Fetching meetings with limit:', limit);

    const { data, error } = await supabase
      .from('meetings')
      .select(`
        *,
        project:projects(
          id,
          name,
          client_name,
          client_email
        )
      `)
      .order('recorded_at', { ascending: false })
      .limit(parseInt(limit));

    if (error) {
      console.error('Error fetching meetings:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch meetings', details: error.message },
        { status: 500 }
      );
    }

    console.log('Meetings fetched successfully:', data?.length || 0, 'meetings');

    return NextResponse.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error in meetings GET:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(request: NextRequest) {
  try {
    const body = await request.json();
    console.log('Creating meeting:', body);

    const {
      title,
      platform,
      recorded_at,
      duration_minutes,
      audio_url,
      transcript_url,
      project_id,
      transcription_status = 'pending'
    } = body;

    // Validate required fields
    if (!title) {
      return NextResponse.json(
        { success: false, error: 'Title is required' },
        { status: 400 }
      );
    }

    // Get user_id from project (for demo purposes, we'll use the project's user_id)
    let user_id = 'ae880596-47ba-447b-8730-c27f05b93b08'; // Default demo user

    if (project_id) {
      const { data: project } = await supabase
        .from('projects')
        .select('user_id')
        .eq('id', project_id)
        .single();

      if (project?.user_id) {
        user_id = project.user_id;
      }
    }

    // Validate and normalize platform value
    const validPlatforms = ['google-meet', 'zoom', 'teams', 'other', 'upload'];
    let normalizedPlatform = platform;

    // Handle common platform variations
    if (platform === 'webex' || platform === 'cisco-webex') {
      normalizedPlatform = 'other';
    } else if (platform === 'skype') {
      normalizedPlatform = 'other';
    } else if (!validPlatforms.includes(platform)) {
      normalizedPlatform = 'other';
    }

    console.log('Platform validation:', { original: platform, normalized: normalizedPlatform });

    // Create meeting (only use columns that exist in current schema)
    const meetingData: any = {
      user_id,
      title,
      platform: normalizedPlatform,
      recorded_at,
      duration_minutes,
      project_id,
      transcription_status
    };

    // Only add audio_url if it exists (this column should exist)
    if (audio_url) {
      meetingData.audio_url = audio_url;
    }

    // For now, store transcript_url in meeting_url field as a workaround
    // TODO: Add proper transcript_url column to database
    if (transcript_url) {
      meetingData.meeting_url = transcript_url;
    }

    const { data, error } = await supabase
      .from('meetings')
      .insert(meetingData)
      .select()
      .single();

    if (error) {
      console.error('Error creating meeting:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create meeting', details: error.message },
        { status: 500 }
      );
    }

    console.log('Meeting created successfully:', data);

    // If audio or transcript files were uploaded, trigger AI processing
    if (audio_url || transcript_url) {
      // TODO: Trigger AI processing for transcription and summary generation
      console.log('AI processing needed for meeting:', data.id);
    }

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in meetings POST:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
