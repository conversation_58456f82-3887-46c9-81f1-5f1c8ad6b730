import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function POST(request: NextRequest) {
  try {
    const authHeader = request.headers.get('authorization');

    if (!authHeader || !authHeader.startsWith('Bearer ')) {
      return NextResponse.json(
        { success: false, error: 'No token provided' },
        { status: 401 }
      );
    }

    const token = authHeader.substring(7);

    // For demo tokens, extract user ID
    if (token.startsWith('demo-token-')) {
      const userId = token.replace('demo-token-', '');
      
      // Get user from database
      const { data: userData, error: userError } = await supabase
        .from('users')
        .select('*')
        .eq('id', userId)
        .single();

      if (userError || !userData) {
        return NextResponse.json(
          { success: false, error: 'User not found' },
          { status: 401 }
        );
      }

      return NextResponse.json({
        success: true,
        user: {
          id: userData.id,
          email: userData.email,
          name: userData.name,
          subscription_tier: userData.subscription_tier
        }
      });
    }

    // For other tokens, you would verify JWT here
    return NextResponse.json(
      { success: false, error: 'Invalid token' },
      { status: 401 }
    );

  } catch (error) {
    console.error('Token verification error:', error);
    return NextResponse.json(
      { success: false, error: 'Invalid or expired token' },
      { status: 401 }
    );
  }
}
