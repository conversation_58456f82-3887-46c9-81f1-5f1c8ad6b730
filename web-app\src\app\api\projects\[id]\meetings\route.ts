import { NextRequest, NextResponse } from 'next/server';
import { createClient } from '@supabase/supabase-js';

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.SUPABASE_SERVICE_ROLE_KEY!
);

export async function GET(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    console.log('Fetching meetings for project:', params.id);

    const { data, error } = await supabase
      .from('meetings')
      .select('*')
      .eq('project_id', params.id)
      .order('recorded_at', { ascending: false });

    if (error) {
      console.error('Error fetching meetings:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to fetch meetings', details: error.message },
        { status: 500 }
      );
    }

    console.log('Meetings fetched successfully:', data?.length || 0, 'meetings');

    return NextResponse.json({
      success: true,
      data: data || []
    });

  } catch (error) {
    console.error('Error in meetings GET:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}

export async function POST(
  request: NextRequest,
  { params }: { params: { id: string } }
) {
  try {
    const body = await request.json();
    console.log('Creating meeting for project:', params.id, body);

    const {
      title,
      description,
      recorded_at,
      duration_minutes,
      audio_file_url,
      transcript,
      summary,
      action_items
    } = body;

    // Validate required fields
    if (!title) {
      return NextResponse.json(
        { success: false, error: 'Title is required' },
        { status: 400 }
      );
    }

    // Get user ID from project
    const { data: project } = await supabase
      .from('projects')
      .select('user_id')
      .eq('id', params.id)
      .single();

    const meetingData = {
      project_id: params.id,
      user_id: project?.user_id || 'user-placeholder',
      title,
      description: description || '',
      recorded_at: recorded_at || new Date().toISOString(),
      duration_minutes: duration_minutes || 0,
      audio_file_url: audio_file_url || null,
      transcript: transcript || null,
      summary: summary || null,
      action_items: action_items || null,
      transcription_status: transcript ? 'completed' : 'pending',
      created_at: new Date().toISOString(),
      updated_at: new Date().toISOString()
    };

    const { data, error } = await supabase
      .from('meetings')
      .insert([meetingData])
      .select('*')
      .single();

    if (error) {
      console.error('Error creating meeting:', error);
      return NextResponse.json(
        { success: false, error: 'Failed to create meeting', details: error.message },
        { status: 500 }
      );
    }

    console.log('Meeting created successfully:', data);

    return NextResponse.json({
      success: true,
      data
    });

  } catch (error) {
    console.error('Error in meetings POST:', error);
    return NextResponse.json(
      { success: false, error: 'Internal server error', details: error instanceof Error ? error.message : 'Unknown error' },
      { status: 500 }
    );
  }
}
