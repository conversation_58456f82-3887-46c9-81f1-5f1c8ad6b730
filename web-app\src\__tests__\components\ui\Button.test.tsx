import { render, screen, fireEvent } from '@testing-library/react'
import '@testing-library/jest-dom'

// Simple Button component for testing
interface ButtonProps {
  children: React.ReactNode
  onClick?: () => void
  disabled?: boolean
  variant?: 'primary' | 'secondary' | 'danger'
  size?: 'sm' | 'md' | 'lg'
  className?: string
}

const Button: React.FC<ButtonProps> = ({
  children,
  onClick,
  disabled = false,
  variant = 'primary',
  size = 'md',
  className = ''
}) => {
  const baseClasses = 'inline-flex items-center justify-center font-medium rounded-lg focus:outline-none focus:ring-2 focus:ring-offset-2'
  
  const variantClasses = {
    primary: 'bg-blue-600 text-white hover:bg-blue-700 focus:ring-blue-500',
    secondary: 'bg-gray-200 text-gray-900 hover:bg-gray-300 focus:ring-gray-500',
    danger: 'bg-red-600 text-white hover:bg-red-700 focus:ring-red-500'
  }
  
  const sizeClasses = {
    sm: 'px-3 py-2 text-sm',
    md: 'px-4 py-2 text-base',
    lg: 'px-6 py-3 text-lg'
  }
  
  const disabledClasses = disabled ? 'opacity-50 cursor-not-allowed' : ''
  
  const classes = `${baseClasses} ${variantClasses[variant]} ${sizeClasses[size]} ${disabledClasses} ${className}`.trim()
  
  return (
    <button
      className={classes}
      onClick={onClick}
      disabled={disabled}
      data-testid="button"
    >
      {children}
    </button>
  )
}

describe('Button Component', () => {
  it('renders with default props', () => {
    render(<Button>Click me</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toBeInTheDocument()
    expect(button).toHaveTextContent('Click me')
    expect(button).not.toBeDisabled()
  })

  it('renders with custom text', () => {
    render(<Button>Custom Button Text</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveTextContent('Custom Button Text')
  })

  it('calls onClick when clicked', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    const button = screen.getByTestId('button')
    fireEvent.click(button)
    
    expect(handleClick).toHaveBeenCalledTimes(1)
  })

  it('does not call onClick when disabled', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick} disabled>Click me</Button>)
    
    const button = screen.getByTestId('button')
    fireEvent.click(button)
    
    expect(handleClick).not.toHaveBeenCalled()
    expect(button).toBeDisabled()
  })

  it('applies primary variant styles by default', () => {
    render(<Button>Primary Button</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveClass('bg-blue-600', 'text-white')
  })

  it('applies secondary variant styles', () => {
    render(<Button variant="secondary">Secondary Button</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveClass('bg-gray-200', 'text-gray-900')
  })

  it('applies danger variant styles', () => {
    render(<Button variant="danger">Danger Button</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveClass('bg-red-600', 'text-white')
  })

  it('applies small size styles', () => {
    render(<Button size="sm">Small Button</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveClass('px-3', 'py-2', 'text-sm')
  })

  it('applies medium size styles by default', () => {
    render(<Button>Medium Button</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveClass('px-4', 'py-2', 'text-base')
  })

  it('applies large size styles', () => {
    render(<Button size="lg">Large Button</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveClass('px-6', 'py-3', 'text-lg')
  })

  it('applies custom className', () => {
    render(<Button className="custom-class">Custom Button</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveClass('custom-class')
  })

  it('applies disabled styles when disabled', () => {
    render(<Button disabled>Disabled Button</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveClass('opacity-50', 'cursor-not-allowed')
    expect(button).toBeDisabled()
  })

  it('renders with complex children', () => {
    render(
      <Button>
        <span>Icon</span>
        <span>Button Text</span>
      </Button>
    )
    
    const button = screen.getByTestId('button')
    expect(button).toHaveTextContent('IconButton Text')
  })

  it('handles multiple clicks', () => {
    const handleClick = jest.fn()
    render(<Button onClick={handleClick}>Click me</Button>)
    
    const button = screen.getByTestId('button')
    fireEvent.click(button)
    fireEvent.click(button)
    fireEvent.click(button)
    
    expect(handleClick).toHaveBeenCalledTimes(3)
  })

  it('maintains focus styles', () => {
    render(<Button>Focus me</Button>)
    
    const button = screen.getByTestId('button')
    expect(button).toHaveClass('focus:outline-none', 'focus:ring-2', 'focus:ring-offset-2')
  })
})
