# KaiNote Database Setup Guide

## 🎯 Quick Setup (5 minutes)

### Step 1: Run Database Schema
1. Open your **Supabase Dashboard**
2. Go to **SQL Editor**
3. Copy and paste the contents of `docs/database-schema.sql`
4. Click **Run** to create all tables

### Step 2: Add Test Data
1. In the same **SQL Editor**
2. Copy and paste the contents of `docs/test-data.sql`
3. Click **Run** to create sample data

### Step 3: Setup Auth Trigger (Optional but Recommended)
1. In the same **SQL Editor**
2. Copy and paste the contents of `docs/auth-trigger.sql`
3. Click **Run** to enable automatic user creation

### Step 4: Add Receipts Table
1. In the same **SQL Editor**
2. Copy and paste the contents of `docs/receipts-table.sql`
3. Click **Run** to add receipts functionality

### Step 5: Test the Application
1. Visit: `http://localhost:3003/projects/550e8400-e29b-41d4-a716-************`
2. You should see the test project with sample data!

---

## 🔐 User Account Creation

### How It Works:
1. **User Signs Up** → Supabase Auth creates record in `auth.users`
2. **Trigger Fires** → Automatically creates record in `public.users`
3. **User Can Use App** → Full access to all features

### Manual User Creation (for testing):
```sql
-- This is already included in test-data.sql
-- Demo user: <EMAIL>
-- Password: (set through Supabase Auth dashboard)
```

### Real User Signup Flow:
```typescript
// Frontend signup (already implemented in your auth system)
const { data, error } = await supabase.auth.signUp({
  email: '<EMAIL>',
  password: 'securepassword',
  options: {
    data: {
      name: 'User Name'
    }
  }
});
// Trigger automatically creates public.users record
```

---

## 📊 Test Data Included

### Projects:
- **E-commerce Website Redesign** (ID: `550e8400-e29b-41d4-a716-************`)
- **Mobile App Development** (ID: `550e8400-e29b-41d4-a716-************`)

### Tasks:
- ✅ **Completed**: Homepage Wireframes
- 🔄 **In Progress**: Design System Creation  
- 📋 **Todo**: Performance Optimization

### Meetings:
- **Project Kickoff Meeting** (Zoom, 60 min)
- **Design Review Session** (Google Meet, 45 min)

### Invoices:
- **INV-2024-001**: $5,000 (Paid)
- **INV-2024-002**: $7,500 (Sent)

### Documents:
- **Project Requirements.pdf**
- **Design Mockups.figma**

---

## 🧪 Testing Checklist

After running the setup, test these features:

### ✅ Project Loading
- [ ] Visit project page - should load without "Failed to load project data"
- [ ] See project overview with statistics
- [ ] View all tabs (Overview, Tasks, Meetings, etc.)

### ✅ Task Management
- [ ] View existing tasks with different statuses
- [ ] Click status circles to change task status
- [ ] Generate AI tasks and add them to project
- [ ] See real-time updates in overview statistics

### ✅ API Integration
- [ ] All data loads from database (not mock data)
- [ ] Task status changes persist after page refresh
- [ ] New tasks appear in task list immediately

### ✅ AI Features
- [ ] Generate tasks from project description
- [ ] Create AI documents
- [ ] Chat interface for document improvement

---

## 🔧 Troubleshooting

### "Failed to load project data"
- ✅ **Fixed**: Run `docs/test-data.sql` to create sample projects

### "Failed to add tasks to project"
- ✅ **Fixed**: All API endpoints are now created and working

### "User not found" errors
- ✅ **Fixed**: Run `docs/auth-trigger.sql` for automatic user creation

### Database connection issues
- Check `.env.local` has correct Supabase credentials
- Verify `SUPABASE_SERVICE_ROLE_KEY` is set

---

## 🚀 Production Deployment

### Environment Variables:
```bash
NEXT_PUBLIC_SUPABASE_URL=your_supabase_url
NEXT_PUBLIC_SUPABASE_ANON_KEY=your_anon_key
SUPABASE_SERVICE_ROLE_KEY=your_service_role_key
OPENAI_API_KEY=your_openai_key
```

### Database Setup:
1. Run `database-schema.sql` in production Supabase
2. Run `auth-trigger.sql` for user management
3. Run `receipts-table.sql` for receipts feature
4. **Skip** `test-data.sql` in production

### Security:
- ✅ Row Level Security enabled on all tables
- ✅ Users can only access their own data
- ✅ API routes use service role key for admin operations

---

## 🎉 Success!

After completing this setup:
- ✅ **Database**: Fully configured with sample data
- ✅ **API**: All endpoints working with real data
- ✅ **Frontend**: Connected to backend APIs
- ✅ **Auth**: Automatic user creation on signup
- ✅ **Features**: Task management, AI generation, receipts

**Your KaiNote application is now production-ready!** 🚀
