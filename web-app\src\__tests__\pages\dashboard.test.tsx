import { render, screen, waitFor } from '@testing-library/react'
import '@testing-library/jest-dom'
import { QueryClient, QueryClientProvider } from 'react-query'

// Mock dashboard component for testing
interface DashboardStats {
  totalProjects: number
  activeProjects: number
  totalRevenue: number
  billableHours: number
}

interface DashboardProps {
  stats?: DashboardStats
  isLoading?: boolean
  error?: string
}

const Dashboard: React.FC<DashboardProps> = ({ stats, isLoading = false, error }) => {
  if (isLoading) {
    return (
      <div data-testid="loading-spinner" className="flex items-center justify-center p-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600"></div>
        <span className="ml-2">Loading dashboard...</span>
      </div>
    )
  }

  if (error) {
    return (
      <div data-testid="error-message" className="p-4 bg-red-100 border border-red-400 text-red-700 rounded-lg">
        <h3 className="font-medium">Error loading dashboard</h3>
        <p>{error}</p>
      </div>
    )
  }

  if (!stats) {
    return (
      <div data-testid="no-data" className="p-8 text-center text-gray-500">
        No data available
      </div>
    )
  }

  return (
    <div data-testid="dashboard" className="p-6">
      <h1 className="text-2xl font-bold text-gray-900 mb-6">Dashboard</h1>
      
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
        <div className="bg-white rounded-lg shadow p-6" data-testid="total-projects-card">
          <div className="flex items-center">
            <div className="bg-blue-100 rounded-lg p-3">
              <svg className="h-6 w-6 text-blue-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Projects</p>
              <p className="text-2xl font-bold text-gray-900" data-testid="total-projects-value">
                {stats.totalProjects}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6" data-testid="active-projects-card">
          <div className="flex items-center">
            <div className="bg-green-100 rounded-lg p-3">
              <svg className="h-6 w-6 text-green-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Active Projects</p>
              <p className="text-2xl font-bold text-gray-900" data-testid="active-projects-value">
                {stats.activeProjects}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6" data-testid="total-revenue-card">
          <div className="flex items-center">
            <div className="bg-yellow-100 rounded-lg p-3">
              <svg className="h-6 w-6 text-yellow-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Total Revenue</p>
              <p className="text-2xl font-bold text-gray-900" data-testid="total-revenue-value">
                ${stats.totalRevenue.toLocaleString()}
              </p>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6" data-testid="billable-hours-card">
          <div className="flex items-center">
            <div className="bg-purple-100 rounded-lg p-3">
              <svg className="h-6 w-6 text-purple-600" fill="none" viewBox="0 0 24 24" stroke="currentColor">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
              </svg>
            </div>
            <div className="ml-4">
              <p className="text-sm font-medium text-gray-600">Billable Hours</p>
              <p className="text-2xl font-bold text-gray-900" data-testid="billable-hours-value">
                {stats.billableHours.toFixed(1)}h
              </p>
            </div>
          </div>
        </div>
      </div>

      <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
        <div className="bg-white rounded-lg shadow p-6" data-testid="recent-projects">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Projects</h2>
          <div className="space-y-3">
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="font-medium text-gray-900">Website Redesign</p>
                <p className="text-sm text-gray-600">Client: Acme Corp</p>
              </div>
              <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-green-100 text-green-800">
                Active
              </span>
            </div>
            <div className="flex items-center justify-between p-3 bg-gray-50 rounded-lg">
              <div>
                <p className="font-medium text-gray-900">Mobile App</p>
                <p className="text-sm text-gray-600">Client: Tech Startup</p>
              </div>
              <span className="inline-flex px-2 py-1 text-xs font-medium rounded-full bg-blue-100 text-blue-800">
                In Progress
              </span>
            </div>
          </div>
        </div>

        <div className="bg-white rounded-lg shadow p-6" data-testid="recent-activity">
          <h2 className="text-lg font-semibold text-gray-900 mb-4">Recent Activity</h2>
          <div className="space-y-3">
            <div className="flex items-start space-x-3">
              <div className="bg-blue-100 rounded-full p-1">
                <svg className="h-4 w-4 text-blue-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-9.293a1 1 0 00-1.414-1.414L9 10.586 7.707 9.293a1 1 0 00-1.414 1.414l2 2a1 1 0 001.414 0l4-4z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">Task completed</p>
                <p className="text-xs text-gray-600">2 hours ago</p>
              </div>
            </div>
            <div className="flex items-start space-x-3">
              <div className="bg-green-100 rounded-full p-1">
                <svg className="h-4 w-4 text-green-600" fill="currentColor" viewBox="0 0 20 20">
                  <path fillRule="evenodd" d="M10 18a8 8 0 100-16 8 8 0 000 16zm3.707-8.293l-3-3a1 1 0 00-1.414 0l-3 3a1 1 0 001.414 1.414L9 9.414V13a1 1 0 102 0V9.414l1.293 1.293a1 1 0 001.414-1.414z" clipRule="evenodd" />
                </svg>
              </div>
              <div>
                <p className="text-sm font-medium text-gray-900">New project created</p>
                <p className="text-xs text-gray-600">1 day ago</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: {
        retry: false,
      },
    },
  })

  return (
    <QueryClientProvider client={queryClient}>
      {children}
    </QueryClientProvider>
  )
}

describe('Dashboard Page', () => {
  const mockStats: DashboardStats = {
    totalProjects: 12,
    activeProjects: 8,
    totalRevenue: 45000,
    billableHours: 156.5
  }

  it('renders dashboard with stats', () => {
    render(
      <TestWrapper>
        <Dashboard stats={mockStats} />
      </TestWrapper>
    )

    expect(screen.getByTestId('dashboard')).toBeInTheDocument()
    expect(screen.getByText('Dashboard')).toBeInTheDocument()
    
    // Check stat cards
    expect(screen.getByTestId('total-projects-value')).toHaveTextContent('12')
    expect(screen.getByTestId('active-projects-value')).toHaveTextContent('8')
    expect(screen.getByTestId('total-revenue-value')).toHaveTextContent('$45,000')
    expect(screen.getByTestId('billable-hours-value')).toHaveTextContent('156.5h')
  })

  it('shows loading state', () => {
    render(
      <TestWrapper>
        <Dashboard isLoading={true} />
      </TestWrapper>
    )

    expect(screen.getByTestId('loading-spinner')).toBeInTheDocument()
    expect(screen.getByText('Loading dashboard...')).toBeInTheDocument()
    expect(screen.queryByTestId('dashboard')).not.toBeInTheDocument()
  })

  it('shows error state', () => {
    const errorMessage = 'Failed to load dashboard data'
    render(
      <TestWrapper>
        <Dashboard error={errorMessage} />
      </TestWrapper>
    )

    expect(screen.getByTestId('error-message')).toBeInTheDocument()
    expect(screen.getByText('Error loading dashboard')).toBeInTheDocument()
    expect(screen.getByText(errorMessage)).toBeInTheDocument()
    expect(screen.queryByTestId('dashboard')).not.toBeInTheDocument()
  })

  it('shows no data state', () => {
    render(
      <TestWrapper>
        <Dashboard />
      </TestWrapper>
    )

    expect(screen.getByTestId('no-data')).toBeInTheDocument()
    expect(screen.getByText('No data available')).toBeInTheDocument()
    expect(screen.queryByTestId('dashboard')).not.toBeInTheDocument()
  })

  it('renders all stat cards with correct labels', () => {
    render(
      <TestWrapper>
        <Dashboard stats={mockStats} />
      </TestWrapper>
    )

    expect(screen.getByText('Total Projects')).toBeInTheDocument()
    expect(screen.getByText('Active Projects')).toBeInTheDocument()
    expect(screen.getByText('Total Revenue')).toBeInTheDocument()
    expect(screen.getByText('Billable Hours')).toBeInTheDocument()
  })

  it('renders recent projects section', () => {
    render(
      <TestWrapper>
        <Dashboard stats={mockStats} />
      </TestWrapper>
    )

    expect(screen.getByTestId('recent-projects')).toBeInTheDocument()
    expect(screen.getByText('Recent Projects')).toBeInTheDocument()
    expect(screen.getByText('Website Redesign')).toBeInTheDocument()
    expect(screen.getByText('Mobile App')).toBeInTheDocument()
  })

  it('renders recent activity section', () => {
    render(
      <TestWrapper>
        <Dashboard stats={mockStats} />
      </TestWrapper>
    )

    expect(screen.getByTestId('recent-activity')).toBeInTheDocument()
    expect(screen.getByText('Recent Activity')).toBeInTheDocument()
    expect(screen.getByText('Task completed')).toBeInTheDocument()
    expect(screen.getByText('New project created')).toBeInTheDocument()
  })

  it('formats revenue with proper locale formatting', () => {
    const statsWithLargeRevenue = {
      ...mockStats,
      totalRevenue: 1234567
    }

    render(
      <TestWrapper>
        <Dashboard stats={statsWithLargeRevenue} />
      </TestWrapper>
    )

    expect(screen.getByTestId('total-revenue-value')).toHaveTextContent('$1,234,567')
  })

  it('formats billable hours with one decimal place', () => {
    const statsWithDecimalHours = {
      ...mockStats,
      billableHours: 123.456
    }

    render(
      <TestWrapper>
        <Dashboard stats={statsWithDecimalHours} />
      </TestWrapper>
    )

    expect(screen.getByTestId('billable-hours-value')).toHaveTextContent('123.5h')
  })

  it('handles zero values correctly', () => {
    const zeroStats = {
      totalProjects: 0,
      activeProjects: 0,
      totalRevenue: 0,
      billableHours: 0
    }

    render(
      <TestWrapper>
        <Dashboard stats={zeroStats} />
      </TestWrapper>
    )

    expect(screen.getByTestId('total-projects-value')).toHaveTextContent('0')
    expect(screen.getByTestId('active-projects-value')).toHaveTextContent('0')
    expect(screen.getByTestId('total-revenue-value')).toHaveTextContent('$0')
    expect(screen.getByTestId('billable-hours-value')).toHaveTextContent('0.0h')
  })
})
