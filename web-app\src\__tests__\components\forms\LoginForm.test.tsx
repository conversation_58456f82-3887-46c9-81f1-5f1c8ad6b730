import { render, screen, fireEvent, waitFor } from '@testing-library/react'
import userEvent from '@testing-library/user-event'
import '@testing-library/jest-dom'
import { useState } from 'react'

// Mock LoginForm component for testing
interface LoginFormProps {
  onSubmit: (data: { email: string; password: string }) => Promise<void>
  isLoading?: boolean
  error?: string
}

const LoginForm: React.FC<LoginFormProps> = ({ onSubmit, isLoading = false, error }) => {
  const [email, setEmail] = useState('')
  const [password, setPassword] = useState('')
  const [validationErrors, setValidationErrors] = useState<{ email?: string; password?: string }>({})

  const validateForm = () => {
    const errors: { email?: string; password?: string } = {}
    
    if (!email) {
      errors.email = 'Email is required'
    } else if (!/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(email)) {
      errors.email = 'Invalid email format'
    }
    
    if (!password) {
      errors.password = 'Password is required'
    } else if (password.length < 6) {
      errors.password = 'Password must be at least 6 characters'
    }
    
    setValidationErrors(errors)
    return Object.keys(errors).length === 0
  }

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    
    if (!validateForm()) {
      return
    }
    
    try {
      await onSubmit({ email, password })
    } catch (err) {
      // Error handling is done by parent component
    }
  }

  return (
    <form onSubmit={handleSubmit} data-testid="login-form">
      <div className="mb-4">
        <label htmlFor="email" className="block text-sm font-medium text-gray-700 mb-2">
          Email
        </label>
        <input
          type="email"
          id="email"
          value={email}
          onChange={(e) => setEmail(e.target.value)}
          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${
            validationErrors.email ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'
          }`}
          placeholder="Enter your email"
          data-testid="email-input"
        />
        {validationErrors.email && (
          <p className="mt-1 text-sm text-red-600" data-testid="email-error">
            {validationErrors.email}
          </p>
        )}
      </div>

      <div className="mb-6">
        <label htmlFor="password" className="block text-sm font-medium text-gray-700 mb-2">
          Password
        </label>
        <input
          type="password"
          id="password"
          value={password}
          onChange={(e) => setPassword(e.target.value)}
          className={`w-full px-3 py-2 border rounded-lg focus:outline-none focus:ring-2 ${
            validationErrors.password ? 'border-red-500 focus:ring-red-500' : 'border-gray-300 focus:ring-blue-500'
          }`}
          placeholder="Enter your password"
          data-testid="password-input"
        />
        {validationErrors.password && (
          <p className="mt-1 text-sm text-red-600" data-testid="password-error">
            {validationErrors.password}
          </p>
        )}
      </div>

      {error && (
        <div className="mb-4 p-3 bg-red-100 border border-red-400 text-red-700 rounded-lg" data-testid="form-error">
          {error}
        </div>
      )}

      <button
        type="submit"
        disabled={isLoading}
        className={`w-full py-2 px-4 rounded-lg font-medium focus:outline-none focus:ring-2 focus:ring-offset-2 ${
          isLoading
            ? 'bg-gray-400 cursor-not-allowed'
            : 'bg-blue-600 hover:bg-blue-700 focus:ring-blue-500'
        } text-white`}
        data-testid="submit-button"
      >
        {isLoading ? 'Signing in...' : 'Sign In'}
      </button>
    </form>
  )
}

describe('LoginForm Component', () => {
  const mockOnSubmit = jest.fn()

  beforeEach(() => {
    mockOnSubmit.mockClear()
  })

  it('renders all form elements', () => {
    render(<LoginForm onSubmit={mockOnSubmit} />)
    
    expect(screen.getByTestId('login-form')).toBeInTheDocument()
    expect(screen.getByTestId('email-input')).toBeInTheDocument()
    expect(screen.getByTestId('password-input')).toBeInTheDocument()
    expect(screen.getByTestId('submit-button')).toBeInTheDocument()
    
    expect(screen.getByLabelText('Email')).toBeInTheDocument()
    expect(screen.getByLabelText('Password')).toBeInTheDocument()
    expect(screen.getByRole('button', { name: 'Sign In' })).toBeInTheDocument()
  })

  it('updates input values when typing', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={mockOnSubmit} />)
    
    const emailInput = screen.getByTestId('email-input')
    const passwordInput = screen.getByTestId('password-input')
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')
    
    expect(emailInput).toHaveValue('<EMAIL>')
    expect(passwordInput).toHaveValue('password123')
  })

  it('shows validation errors for empty fields', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={mockOnSubmit} />)
    
    const submitButton = screen.getByTestId('submit-button')
    await user.click(submitButton)
    
    expect(screen.getByTestId('email-error')).toHaveTextContent('Email is required')
    expect(screen.getByTestId('password-error')).toHaveTextContent('Password is required')
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('shows validation error for invalid email format', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={mockOnSubmit} />)

    const emailInput = screen.getByTestId('email-input')
    const passwordInput = screen.getByTestId('password-input')
    const submitButton = screen.getByTestId('submit-button')

    await user.type(emailInput, 'invalid-email')
    await user.type(passwordInput, 'validpassword') // Add valid password to avoid multiple errors
    await user.click(submitButton)

    expect(screen.getByTestId('email-error')).toHaveTextContent('Invalid email format')
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('shows validation error for short password', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={mockOnSubmit} />)
    
    const passwordInput = screen.getByTestId('password-input')
    const submitButton = screen.getByTestId('submit-button')
    
    await user.type(passwordInput, '123')
    await user.click(submitButton)
    
    expect(screen.getByTestId('password-error')).toHaveTextContent('Password must be at least 6 characters')
    expect(mockOnSubmit).not.toHaveBeenCalled()
  })

  it('submits form with valid data', async () => {
    const user = userEvent.setup()
    mockOnSubmit.mockResolvedValue(undefined)
    
    render(<LoginForm onSubmit={mockOnSubmit} />)
    
    const emailInput = screen.getByTestId('email-input')
    const passwordInput = screen.getByTestId('password-input')
    const submitButton = screen.getByTestId('submit-button')
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')
    await user.click(submitButton)
    
    expect(mockOnSubmit).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123'
    })
  })

  it('shows loading state when submitting', () => {
    render(<LoginForm onSubmit={mockOnSubmit} isLoading={true} />)
    
    const submitButton = screen.getByTestId('submit-button')
    
    expect(submitButton).toBeDisabled()
    expect(submitButton).toHaveTextContent('Signing in...')
    expect(submitButton).toHaveClass('bg-gray-400', 'cursor-not-allowed')
  })

  it('displays error message when provided', () => {
    const errorMessage = 'Invalid credentials'
    render(<LoginForm onSubmit={mockOnSubmit} error={errorMessage} />)
    
    const errorElement = screen.getByTestId('form-error')
    expect(errorElement).toBeInTheDocument()
    expect(errorElement).toHaveTextContent(errorMessage)
  })

  it('clears validation errors when user starts typing', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={mockOnSubmit} />)
    
    // First trigger validation errors
    const submitButton = screen.getByTestId('submit-button')
    await user.click(submitButton)
    
    expect(screen.getByTestId('email-error')).toBeInTheDocument()
    
    // Then start typing to clear errors
    const emailInput = screen.getByTestId('email-input')
    await user.type(emailInput, '<EMAIL>')
    
    // Click submit again to trigger validation
    await user.click(submitButton)
    
    expect(screen.queryByTestId('email-error')).not.toBeInTheDocument()
  })

  it('handles form submission with Enter key', async () => {
    const user = userEvent.setup()
    mockOnSubmit.mockResolvedValue(undefined)
    
    render(<LoginForm onSubmit={mockOnSubmit} />)
    
    const emailInput = screen.getByTestId('email-input')
    const passwordInput = screen.getByTestId('password-input')
    
    await user.type(emailInput, '<EMAIL>')
    await user.type(passwordInput, 'password123')
    await user.keyboard('{Enter}')
    
    expect(mockOnSubmit).toHaveBeenCalledWith({
      email: '<EMAIL>',
      password: 'password123'
    })
  })

  it('applies error styles to inputs with validation errors', async () => {
    const user = userEvent.setup()
    render(<LoginForm onSubmit={mockOnSubmit} />)
    
    const submitButton = screen.getByTestId('submit-button')
    await user.click(submitButton)
    
    const emailInput = screen.getByTestId('email-input')
    const passwordInput = screen.getByTestId('password-input')
    
    expect(emailInput).toHaveClass('border-red-500', 'focus:ring-red-500')
    expect(passwordInput).toHaveClass('border-red-500', 'focus:ring-red-500')
  })
})
